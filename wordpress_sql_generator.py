#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WordPress SQL Generator
Creates SQL file for direct database import with proper categories
"""

import json
import re
from datetime import datetime
import hashlib

class WordPressSQLGenerator:
    """Generate SQL for direct WordPress database import"""

    def __init__(self, structure_file='book_structure.json', table_prefix='wp_'):
        self.structure = None
        self.table_prefix = table_prefix
        self.sql_statements = []
        self.category_id_counter = 1
        self.post_id_counter = 1
        self.term_taxonomy_id_counter = 1
        self.load_structure(structure_file)

    def load_structure(self, file_path):
        """Load structure from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.structure = json.load(f)
            print(f"✅ تم تحميل البنية من: {file_path}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False

    def escape_sql(self, text):
        """Escape text for SQL"""
        if not text:
            return ''
        return text.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')

    def generate_slug(self, text):
        """Generate URL-friendly slug"""
        # Remove Arabic diacritics and special characters
        text = re.sub(r'[^\w\s-]', '', text)
        text = re.sub(r'[-\s]+', '-', text)
        return text.strip('-').lower()[:50]

    def add_sql_comment(self, comment):
        """Add SQL comment"""
        self.sql_statements.append(f"\n-- {comment}")
        self.sql_statements.append("-- " + "="*50)

    def generate_categories_sql(self):
        """Generate SQL for categories (terms and term_taxonomy)"""
        self.add_sql_comment("إنشاء التصنيفات (جميع الأبواب والفصول)")

        # Create parent categories (ALL parts)
        for part in self.structure['parts']:  # ALL parts
            term_id = self.category_id_counter
            slug = self.generate_slug(part['title']) or f"part-{part['id']}"
            name = self.escape_sql(part['title'][:150])  # Increased length

            # Insert into wp_terms
            self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}terms (term_id, name, slug, term_group)
VALUES ({term_id}, '{name}', '{slug}', 0);""")

            # Insert into wp_term_taxonomy
            self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count)
VALUES ({self.term_taxonomy_id_counter}, {term_id}, 'category', '', 0, 0);""")

            # Store mapping for later use
            part['wp_term_id'] = term_id
            part['wp_term_taxonomy_id'] = self.term_taxonomy_id_counter

            self.category_id_counter += 1
            self.term_taxonomy_id_counter += 1

        # Create child categories (ALL chapters)
        for chapter in self.structure['chapters']:  # ALL chapters
            if chapter.get('part_id'):
                term_id = self.category_id_counter
                slug = self.generate_slug(chapter['title']) or f"chapter-{chapter['id']}"
                name = self.escape_sql(chapter['title'][:150])  # Increased length

                # Find parent part
                parent_part = next((p for p in self.structure['parts'] if p['id'] == chapter['part_id']), None)
                parent_id = parent_part['wp_term_taxonomy_id'] if parent_part else 0

                # Insert into wp_terms
                self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}terms (term_id, name, slug, term_group)
VALUES ({term_id}, '{name}', '{slug}', 0);""")

                # Insert into wp_term_taxonomy
                self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count)
VALUES ({self.term_taxonomy_id_counter}, {term_id}, 'category', '', {parent_id}, 0);""")

                # Store mapping
                chapter['wp_term_id'] = term_id
                chapter['wp_term_taxonomy_id'] = self.term_taxonomy_id_counter

                self.category_id_counter += 1
                self.term_taxonomy_id_counter += 1

    def generate_posts_sql(self):
        """Generate SQL for posts"""
        self.add_sql_comment("إنشاء جميع المقالات من الكتاب")

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Process ALL content units
        for content_unit in self.structure['content']:  # ALL content
            post_id = self.post_id_counter

            # Prepare post data
            title = self.escape_sql(content_unit['title'][:250])  # Increased length
            slug = self.generate_slug(content_unit['title']) or f"post-{post_id}"

            # Build content - include more content per post
            content_html = ""
            for item in content_unit.get('content', [])[:5]:  # Increased to 5 items per post
                if item['type'] == 'content':
                    text = self.escape_sql(item['text'][:2000])  # Increased text length
                    content_html += f"<p>{text}</p>\\n"
                elif item['type'] == 'summary':
                    text = self.escape_sql(item['text'][:1000])  # Increased summary length
                    content_html += f"<div class='chapter-summary'><h3>خلاصة الفصل</h3><p>{text}</p></div>\\n"

            # Create excerpt from first content item
            excerpt = ""
            if content_unit.get('content'):
                first_content = content_unit['content'][0]
                if first_content.get('text'):
                    excerpt = self.escape_sql(first_content['text'][:300])

            # Insert post
            self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}posts (
    ID, post_author, post_date, post_date_gmt, post_content, post_title,
    post_excerpt, post_status, comment_status, ping_status, post_password,
    post_name, to_ping, pinged, post_modified, post_modified_gmt,
    post_content_filtered, post_parent, guid, menu_order, post_type,
    post_mime_type, comment_count
) VALUES (
    {post_id}, 1, '{current_time}', '{current_time}', '{content_html}', '{title}',
    '{excerpt}', 'publish', 'open', 'open', '', '{slug}',
    '', '', '{current_time}', '{current_time}',
    '', 0, 'http://localhost/?p={post_id}', 0, 'post',
    '', 0
);""")

            # Assign category to post
            if content_unit.get('chapter_id'):
                chapter = next((c for c in self.structure['chapters']
                              if c['id'] == content_unit['chapter_id'] and 'wp_term_taxonomy_id' in c), None)
                if chapter:
                    self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}term_relationships (object_id, term_taxonomy_id, term_order)
VALUES ({post_id}, {chapter['wp_term_taxonomy_id']}, 0);""")

            # Also assign to parent part if available
            elif content_unit.get('part_id'):
                part = next((p for p in self.structure['parts']
                           if p['id'] == content_unit['part_id'] and 'wp_term_taxonomy_id' in p), None)
                if part:
                    self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}term_relationships (object_id, term_taxonomy_id, term_order)
VALUES ({post_id}, {part['wp_term_taxonomy_id']}, 0);""")

            self.post_id_counter += 1

    def generate_options_sql(self):
        """Generate SQL for WordPress options"""
        self.add_sql_comment("تحديث إعدادات WordPress")

        # Update site language to Arabic
        self.sql_statements.append(f"""
UPDATE {self.table_prefix}options
SET option_value = 'ar'
WHERE option_name = 'WPLANG';""")

        # Update site title
        self.sql_statements.append(f"""
UPDATE {self.table_prefix}options
SET option_value = 'مشروع النهضة وبناء الدولة السورية'
WHERE option_name = 'blogname';""")

        # Update site description
        self.sql_statements.append(f"""
UPDATE {self.table_prefix}options
SET option_value = 'كتاب مشروع النهضة وبناء الدولة السورية - ما بعد الاستبداد'
WHERE option_name = 'blogdescription';""")

        # Set default category to first created category
        if self.structure['parts']:
            first_part = self.structure['parts'][0]
            if 'wp_term_id' in first_part:
                self.sql_statements.append(f"""
UPDATE {self.table_prefix}options
SET option_value = '{first_part['wp_term_id']}'
WHERE option_name = 'default_category';""")

    def generate_complete_sql(self):
        """Generate complete SQL file"""
        print("🔧 جاري إنشاء ملف SQL...")

        # Add header
        self.sql_statements.append("-- WordPress SQL Import for Nahda Book")
        self.sql_statements.append("-- Generated on: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        self.sql_statements.append("-- Encoding: UTF-8")
        self.sql_statements.append("\nSET NAMES utf8mb4;")
        self.sql_statements.append("SET FOREIGN_KEY_CHECKS = 0;")

        # Generate categories
        self.generate_categories_sql()

        # Generate posts
        self.generate_posts_sql()

        # Generate options
        self.generate_options_sql()

        # Update category counts
        self.add_sql_comment("تحديث عدد المقالات في التصنيفات")
        self.sql_statements.append(f"""
UPDATE {self.table_prefix}term_taxonomy tt
SET count = (
    SELECT COUNT(*)
    FROM {self.table_prefix}term_relationships tr
    WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
)
WHERE tt.taxonomy = 'category';""")

        # Add footer
        self.sql_statements.append("\nSET FOREIGN_KEY_CHECKS = 1;")
        self.sql_statements.append("-- End of SQL import")
        self.sql_statements.append("-- Total content imported successfully!")

        print("✅ تم إنشاء SQL بنجاح")
        return True

    def save_sql_file(self, filename='wordpress_import.sql'):
        """Save SQL to file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.sql_statements))

            print(f"✅ تم حفظ ملف SQL: {filename}")

            # Show statistics
            parts_count = len(self.structure['parts'])  # ALL parts
            chapters_count = len([c for c in self.structure['chapters'] if c.get('part_id')])  # ALL chapters
            posts_count = len(self.structure['content'])  # ALL content

            print(f"📊 إحصائيات SQL الشامل:")
            print(f"   📚 إجمالي الأبواب: {parts_count}")
            print(f"   📖 إجمالي الفصول: {chapters_count}")
            print(f"   📄 إجمالي المقالات: {posts_count}")
            print(f"   📝 إجمالي الكلمات المقدرة: {posts_count * 150:,}")  # Rough estimate

            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ ملف SQL: {e}")
            return False

    def create_import_instructions(self):
        """Create SQL import instructions"""
        instructions = f"""
# تعليمات استيراد ملف SQL إلى WordPress

## الطريقة الأولى: phpMyAdmin
1. ادخل إلى phpMyAdmin
2. اختر قاعدة بيانات WordPress
3. اذهب إلى تبويب "Import"
4. اختر ملف wordpress_import.sql
5. تأكد من Character set: utf8mb4_unicode_ci
6. اضغط "Go"

## الطريقة الثانية: سطر الأوامر
```bash
mysql -u username -p database_name < wordpress_import.sql
```

## الطريقة الثالثة: إضافة WordPress
استخدم إضافة "WP-DB Manager" أو "phpMyAdmin"

## بعد الاستيراد:
1. تحقق من Posts > Categories
2. تحقق من Posts > All Posts
3. تأكد من ظهور التصنيفات الصحيحة
4. اختبر عرض المقالات

## ملاحظات مهمة:
- تأكد من أن قاعدة البيانات تدعم utf8mb4
- انشئ نسخة احتياطية قبل الاستيراد
- تأكد من صلاحيات المستخدم للكتابة في قاعدة البيانات
"""

        with open('sql_import_instructions.md', 'w', encoding='utf-8') as f:
            f.write(instructions)

        print("✅ تم إنشاء تعليمات الاستيراد: sql_import_instructions.md")

def main():
    """Main function"""
    print("🔧 مولد SQL لاستيراد كتاب النهضة إلى WordPress")
    print("=" * 60)

    generator = WordPressSQLGenerator()

    if not generator.structure:
        print("❌ لم يتم العثور على ملف البنية")
        return False

    # Generate SQL
    if generator.generate_complete_sql():
        if generator.save_sql_file():
            generator.create_import_instructions()

            print("\n" + "=" * 60)
            print("✅ تم إنشاء ملف SQL بنجاح!")
            print("📁 الملفات المُنشأة:")
            print("   - wordpress_import.sql (ملف SQL للاستيراد)")
            print("   - sql_import_instructions.md (تعليمات الاستيراد)")

            print("\n🎯 المميزات:")
            print("   ✅ تصنيفات هرمية صحيحة")
            print("   ✅ ربط المقالات بالتصنيفات")
            print("   ✅ نص عربي مع UTF-8")
            print("   ✅ إعدادات WordPress محدثة")

            print("\n⚠️ تذكير:")
            print("   - انشئ نسخة احتياطية قبل الاستيراد")
            print("   - تأكد من دعم utf8mb4 في قاعدة البيانات")

            return True

    return False

if __name__ == "__main__":
    main()
