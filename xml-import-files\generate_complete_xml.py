#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد ملفات XML للمقالات الكاملة
إنشاء ملفات XML محسنة للمقالات الكاملة
"""

import json
import os
from datetime import datetime

class CompleteXMLGenerator:
    """مولد ملفات XML للمقالات الكاملة"""

    def __init__(self):
        self.wp_structure = None
        self.xml_files = []

    def load_complete_structure(self):
        """تحميل بنية WordPress للمقالات الكاملة"""
        try:
            # محاولة تحميل النسخة المحسنة أولاً
            if os.path.exists('book_structure_enhanced.json'):
                with open('book_structure_enhanced.json', 'r', encoding='utf-8') as f:
                    enhanced_structure = json.load(f)
                # تحويل إلى بنية WordPress
                self.wp_structure = self.convert_enhanced_to_wp(enhanced_structure)
            else:
                with open('wordpress_structure_complete.json', 'r', encoding='utf-8') as f:
                    self.wp_structure = json.load(f)

            print(f"✅ تم تحميل البنية الكاملة:")
            print(f"   📂 التصنيفات: {len(self.wp_structure['categories'])}")
            print(f"   📄 المقالات الكاملة: {len(self.wp_structure['posts'])}")

            # إحصائيات الكلمات
            if self.wp_structure['posts']:
                word_counts = [post.get('word_count', 0) for post in self.wp_structure['posts']]
                avg_words = sum(word_counts) / len(word_counts)
                min_words = min(word_counts)
                max_words = max(word_counts)

                print(f"   📊 متوسط الكلمات: {avg_words:.0f} كلمة/مقال")
                print(f"   📉 أقل مقال: {min_words} كلمة")
                print(f"   📈 أكبر مقال: {max_words} كلمة")

            return True

        except FileNotFoundError:
            print("❌ لم يتم العثور على ملف البنية الكاملة. يرجى تشغيل create_complete_articles.py أولاً")
            return False
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False

    def convert_enhanced_to_wp(self, enhanced_structure):
        """تحويل البنية المحسنة إلى بنية WordPress"""
        wp_categories = []
        wp_posts = []

        # إنشاء التصنيفات
        category_id = 1

        # الأقسام الرئيسية
        for part in enhanced_structure['parts']:
            category = {
                'id': category_id,
                'name': part['title'][:80],  # تحديد الطول
                'slug': self.create_slug(part['title']),
                'parent': 0,
                'level': 1,
                'type': 'part',
                'original_id': part['id']
            }
            wp_categories.append(category)
            category_id += 1

        # الأبواب
        for chapter in enhanced_structure['chapters']:
            parent_wp_id = 0
            if chapter.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == 'part' and cat['original_id'] == chapter['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']

            category = {
                'id': category_id,
                'name': chapter['title'][:80],
                'slug': self.create_slug(chapter['title']),
                'parent': parent_wp_id,
                'level': 2,
                'type': 'chapter',
                'original_id': chapter['id']
            }
            wp_categories.append(category)
            category_id += 1

        # الفصول
        for section in enhanced_structure['sections']:
            parent_wp_id = 0
            if section.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == 'chapter' and cat['original_id'] == section['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']

            category = {
                'id': category_id,
                'name': section['title'][:80],
                'slug': self.create_slug(section['title']),
                'parent': parent_wp_id,
                'level': 3,
                'type': 'section',
                'original_id': section['id']
            }
            wp_categories.append(category)
            category_id += 1

        # المباحث
        for subsection in enhanced_structure['subsections']:
            parent_wp_id = 0
            if subsection.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == 'section' and cat['original_id'] == subsection['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']

            category = {
                'id': category_id,
                'name': subsection['title'][:80],
                'slug': self.create_slug(subsection['title']),
                'parent': parent_wp_id,
                'level': 4,
                'type': 'subsection',
                'original_id': subsection['id']
            }
            wp_categories.append(category)
            category_id += 1

        # إنشاء المقالات الكاملة
        for article in enhanced_structure['articles']:
            # تحديد التصنيف
            category_id = 1  # افتراضي

            if article.get('parent_type') and article.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == article['parent_type'] and cat['original_id'] == article['parent_id']), None)
                if parent_cat:
                    category_id = parent_cat['id']

            post = {
                'id': article['id'],
                'title': article['title'][:200],  # تحديد طول العنوان
                'content': article['content'],
                'category_id': category_id,
                'slug': self.create_slug(article['title']),
                'excerpt': article.get('excerpt', ''),
                'word_count': article.get('word_count', 0)
            }
            wp_posts.append(post)

        return {
            'categories': wp_categories,
            'posts': wp_posts
        }

    def create_slug(self, text):
        """إنشاء slug للروابط"""
        import re
        # تنظيف النص
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-').lower()[:50]

    def create_xml_header(self):
        """إنشاء رأس ملف XML"""
        return '''<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0"
    xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:wfw="http://wellformedweb.org/CommentAPI/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:wp="http://wordpress.org/export/1.2/">

<channel>
    <title>مشروع النهضة السورية - مقالات كاملة</title>
    <link>http://localhost</link>
    <description>كتاب مشروع النهضة وبناء الدولة السورية - مقالات كاملة</description>
    <pubDate>{}</pubDate>
    <language>ar</language>
    <wp:wxr_version>1.2</wp:wxr_version>
    <wp:base_site_url>http://localhost</wp:base_site_url>
    <wp:base_blog_url>http://localhost</wp:base_blog_url>

    <wp:author>
        <wp:author_id>1</wp:author_id>
        <wp:author_login>admin</wp:author_login>
        <wp:author_email><EMAIL></wp:author_email>
        <wp:author_display_name><![CDATA[مؤلف النهضة]]></wp:author_display_name>
        <wp:author_first_name><![CDATA[]]></wp:author_first_name>
        <wp:author_last_name><![CDATA[]]></wp:author_last_name>
    </wp:author>
'''.format(datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000'))

    def create_xml_footer(self):
        """إنشاء تذييل ملف XML"""
        return '''
</channel>
</rss>'''

    def escape_xml(self, text):
        """تنظيف النص لـ XML"""
        if not text:
            return ''
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#39;'))

    def create_category_xml(self, category):
        """إنشاء XML للتصنيف"""
        cat_id = category['id']
        name = self.escape_xml(category['name'])
        slug = category['slug']

        # تحديد الأب
        parent_slug = ''
        if category['parent'] > 0:
            parent_cat = next((cat for cat in self.wp_structure['categories']
                             if cat['id'] == category['parent']), None)
            if parent_cat:
                parent_slug = parent_cat['slug']

        # وصف حسب النوع
        descriptions = {
            'part': 'قسم رئيسي من مشروع النهضة',
            'chapter': 'باب من أبواب المشروع',
            'section': 'فصل تفصيلي',
            'subsection': 'مبحث أو محور فرعي'
        }
        description = descriptions.get(category.get('type', ''), 'تصنيف من كتاب النهضة')

        return f'''
    <wp:category>
        <wp:term_id>{cat_id}</wp:term_id>
        <wp:category_nicename>{slug}</wp:category_nicename>
        <wp:category_parent>{parent_slug}</wp:category_parent>
        <wp:cat_name><![CDATA[{name}]]></wp:cat_name>
        <wp:category_description><![CDATA[{description}]]></wp:category_description>
    </wp:category>'''

    def create_post_xml(self, post, post_id):
        """إنشاء XML للمقال الكامل"""
        title = self.escape_xml(post['title'][:200])
        content = post['content']  # المحتوى الكامل
        excerpt = post.get('excerpt', '')[:500]  # مقتطف أطول
        slug = post['slug'][:60]
        word_count = post.get('word_count', 0)

        # العثور على التصنيف
        category_slug = 'uncategorized'
        category_name = 'غير مصنف'

        if post.get('category_id'):
            category = next((cat for cat in self.wp_structure['categories']
                           if cat['id'] == post['category_id']), None)
            if category:
                category_slug = category['slug']
                category_name = category['name']

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        return f'''
    <item>
        <title><![CDATA[{title}]]></title>
        <link>http://localhost/?p={post_id}</link>
        <pubDate>{datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}</pubDate>
        <dc:creator><![CDATA[admin]]></dc:creator>
        <guid isPermaLink="false">http://localhost/?p={post_id}</guid>
        <description></description>
        <content:encoded><![CDATA[{content}]]></content:encoded>
        <excerpt:encoded><![CDATA[{excerpt}]]></excerpt:encoded>
        <wp:post_id>{post_id}</wp:post_id>
        <wp:post_date><![CDATA[{current_time}]]></wp:post_date>
        <wp:post_date_gmt><![CDATA[{current_time}]]></wp:post_date_gmt>
        <wp:comment_status><![CDATA[open]]></wp:comment_status>
        <wp:ping_status><![CDATA[open]]></wp:ping_status>
        <wp:post_name><![CDATA[{slug}]]></wp:post_name>
        <wp:status><![CDATA[publish]]></wp:status>
        <wp:post_parent>0</wp:post_parent>
        <wp:menu_order>0</wp:menu_order>
        <wp:post_type><![CDATA[post]]></wp:post_type>
        <wp:post_password><![CDATA[]]></wp:post_password>
        <wp:is_sticky>0</wp:is_sticky>
        <category domain="category" nicename="{category_slug}"><![CDATA[{category_name}]]></category>
        <wp:postmeta>
            <wp:meta_key><![CDATA[word_count]]></wp:meta_key>
            <wp:meta_value><![CDATA[{word_count}]]></wp:meta_value>
        </wp:postmeta>
    </item>'''

    def create_test_file_complete(self):
        """إنشاء ملف اختبار للمقالات الكاملة"""
        print("🧪 إنشاء ملف اختبار للمقالات الكاملة...")

        content = self.create_xml_header()

        # إضافة عينة من التصنيفات (15 تصنيف)
        sample_categories = self.wp_structure['categories'][:15]
        for category in sample_categories:
            content += self.create_category_xml(category)

        # إضافة عينة من المقالات الكاملة (5 مقالات)
        sample_posts = self.wp_structure['posts'][:5]
        for i, post in enumerate(sample_posts):
            content += self.create_post_xml(post, i + 1)

        content += self.create_xml_footer()

        filename = 'test_complete_articles.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({size:.1f} KB)")
        print(f"   📊 المحتوى: {len(sample_categories)} تصنيف + {len(sample_posts)} مقال كامل")

        # إحصائيات المقالات
        if sample_posts:
            word_counts = [post.get('word_count', 0) for post in sample_posts]
            print(f"   📈 كلمات المقالات: {min(word_counts)}-{max(word_counts)} كلمة")

        return filename

    def create_complete_categories_file(self):
        """إنشاء ملف التصنيفات الكامل"""
        print("📂 إنشاء ملف التصنيفات الكامل...")

        content = self.create_xml_header()

        # ترتيب التصنيفات حسب المستوى
        sorted_categories = sorted(self.wp_structure['categories'],
                                 key=lambda x: (x.get('level', 1), x.get('id', 0)))

        for category in sorted_categories:
            content += self.create_category_xml(category)

        content += self.create_xml_footer()

        filename = 'complete_categories.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        self.xml_files.append(filename)
        size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({size:.1f} KB - {len(sorted_categories)} تصنيف)")
        return filename

    def create_complete_posts_files(self, max_file_size_mb=2):
        """إنشاء ملفات المقالات الكاملة"""
        print(f"📄 إنشاء ملفات المقالات الكاملة (حد أقصى {max_file_size_mb} MB لكل ملف)...")

        posts = self.wp_structure['posts']
        current_file_posts = []
        current_file_size = 0
        file_number = 1
        max_size_bytes = max_file_size_mb * 1024 * 1024

        for post in posts:
            # تقدير حجم المقال
            post_size = len(post['content'].encode('utf-8'))

            # إذا تجاوز الحد الأقصى، احفظ الملف الحالي وابدأ جديد
            if current_file_size + post_size > max_size_bytes and current_file_posts:
                self.save_posts_file(current_file_posts, file_number)
                current_file_posts = []
                current_file_size = 0
                file_number += 1

            current_file_posts.append(post)
            current_file_size += post_size

        # حفظ آخر ملف
        if current_file_posts:
            self.save_posts_file(current_file_posts, file_number)

        return file_number

    def save_posts_file(self, posts, file_number):
        """حفظ ملف مقالات"""
        content = self.create_xml_header()

        for i, post in enumerate(posts):
            post_id = (file_number - 1) * 1000 + i + 1  # ID فريد
            content += self.create_post_xml(post, post_id)

        content += self.create_xml_footer()

        filename = f'complete_posts_part_{file_number:02d}.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        self.xml_files.append(filename)
        size = os.path.getsize(filename) / 1024

        # إحصائيات الكلمات
        word_counts = [post.get('word_count', 0) for post in posts]
        avg_words = sum(word_counts) / len(word_counts) if word_counts else 0

        print(f"✅ تم إنشاء: {filename} ({size:.1f} KB - {len(posts)} مقال)")
        print(f"   📊 متوسط الكلمات: {avg_words:.0f} كلمة/مقال")
        print(f"   📈 نطاق الكلمات: {min(word_counts)}-{max(word_counts)} كلمة")

    def create_import_guide_complete(self, posts_files_count):
        """إنشاء دليل الاستيراد للمقالات الكاملة"""
        guide = f"""# 🚀 دليل استيراد المقالات الكاملة - كتاب النهضة

## 🎯 المميزات الجديدة

### ✅ **مقالات كاملة:**
- كل مقال يحتوي على المحتوى الكامل للفصل/المبحث
- لا توجد مقاطع مقطعة أو محتوى ناقص
- أحجام متنوعة: من 500 إلى 3000+ كلمة حسب الفصل

### ✅ **جودة محسنة:**
- محتوى كامل ومتماسك
- عناوين واضحة ومحددة
- تصنيف دقيق حسب البنية الهرمية

## 📁 الملفات الجاهزة

### 🧪 **ملف الاختبار:**
- `test_complete_articles.xml` - اختبار المقالات الكاملة

### 📂 **ملف التصنيفات:**
- `complete_categories.xml` - جميع التصنيفات الهرمية

### 📄 **ملفات المقالات الكاملة:**
- `complete_posts_part_01.xml` إلى `complete_posts_part_{posts_files_count:02d}.xml` - المقالات الكاملة

## 🚀 خطوات الاستيراد

### 1️⃣ **اختبار المقالات الكاملة (5 دقائق):**
```
أدوات → استيراد → WordPress → test_complete_articles.xml
```

### 2️⃣ **استيراد التصنيفات (10 دقائق):**
```
أدوات → استيراد → WordPress → complete_categories.xml
```

### 3️⃣ **استيراد المقالات الكاملة ({posts_files_count * 10}-{posts_files_count * 15} دقيقة):**
```
أدوات → استيراد → WordPress → complete_posts_part_01.xml
أدوات → استيراد → WordPress → complete_posts_part_02.xml
... (استمر بالترتيب حتى complete_posts_part_{posts_files_count:02d}.xml)
```

## 📊 الإحصائيات

| العنصر | العدد | الوقت المتوقع |
|---------|-------|---------------|
| 📂 التصنيفات | {len(self.wp_structure['categories'])} | 10 دقائق |
| 📄 المقالات الكاملة | {len(self.wp_structure['posts'])} | {posts_files_count * 10}-{posts_files_count * 15} دقائق |
| **المجموع** | **{len(self.wp_structure['categories']) + len(self.wp_structure['posts'])}** | **{20 + posts_files_count * 10}-{25 + posts_files_count * 15} دقائق** |

## 🎯 الفرق عن النسخة السابقة

### ❌ **النسخة السابقة:**
- مقالات مقطعة (1000 كلمة كحد أقصى)
- محتوى ناقص ومجزأ
- 1,560 مقال صغير

### ✅ **النسخة الجديدة:**
- مقالات كاملة (500-3000+ كلمة)
- محتوى متكامل ومتماسك
- {len(self.wp_structure['posts'])} مقال كامل

## ⚠️ تعليمات مهمة

1. **استخدم الملفات الجديدة** بادئة `complete_`
2. **لا تخلط** مع الملفات السابقة
3. **انتظر اكتمال** كل ملف قبل الانتقال للتالي
4. **احتفظ بنسخة احتياطية** قبل البدء

---

**🎉 بعد الاستيراد ستحصل على موقع WordPress بمقالات كاملة ومتماسكة!**
"""

        with open('COMPLETE_IMPORT_GUIDE.md', 'w', encoding='utf-8') as f:
            f.write(guide)

        print("✅ تم إنشاء: COMPLETE_IMPORT_GUIDE.md")

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء ملفات XML للمقالات الكاملة")
    print("=" * 50)

    generator = CompleteXMLGenerator()

    # تحميل البنية الكاملة
    if not generator.load_complete_structure():
        return False

    print(f"\n🔧 إنشاء ملفات XML للمقالات الكاملة...")

    # إنشاء ملف الاختبار
    generator.create_test_file_complete()

    # إنشاء ملف التصنيفات
    generator.create_complete_categories_file()

    # إنشاء ملفات المقالات الكاملة
    posts_files_count = generator.create_complete_posts_files(2)  # 2 MB لكل ملف

    # إنشاء دليل الاستيراد
    generator.create_import_guide_complete(posts_files_count)

    # الملخص النهائي
    print(f"\n" + "=" * 50)
    print("🎉 تم إنشاء ملفات XML للمقالات الكاملة بنجاح!")

    all_files = ['test_complete_articles.xml', 'complete_categories.xml'] + generator.xml_files
    total_size = 0

    print(f"\n📁 الملفات المُنشأة:")
    for xml_file in all_files:
        if os.path.exists(xml_file):
            size = os.path.getsize(xml_file) / 1024
            total_size += size
            print(f"   - {xml_file} ({size:.1f} KB)")

    print(f"\n📊 الإحصائيات النهائية:")
    print(f"   📁 إجمالي الملفات: {len(all_files)}")
    print(f"   💾 إجمالي الحجم: {total_size:.1f} KB ({total_size/1024:.1f} MB)")
    print(f"   📂 التصنيفات: {len(generator.wp_structure['categories'])}")
    print(f"   📄 المقالات الكاملة: {len(generator.wp_structure['posts'])}")

    print(f"\n🎯 الخطوات التالية:")
    print(f"   1. اقرأ COMPLETE_IMPORT_GUIDE.md للتعليمات")
    print(f"   2. ابدأ بـ test_complete_articles.xml للاختبار")
    print(f"   3. استورد complete_categories.xml للتصنيفات")
    print(f"   4. استورد ملفات complete_posts_part بالترتيب")

    return True

if __name__ == "__main__":
    main()
