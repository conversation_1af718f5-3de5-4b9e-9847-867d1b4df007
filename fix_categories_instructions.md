
# تعليمات إصلاح التصنيفات في WordPress

## المشكلة:
جميع المقالات تظهر في تصنيف "Uncategorized" بدلاً من التصنيفات الصحيحة.

## الحل:
استخدام ملف SQL لإصلاح التصنيفات مباشرة في قاعدة البيانات.

## خطوات التطبيق:

### 1. إنشاء نسخة احتياطية:
```sql
-- في phpMyAdmin أو سطر الأوامر
mysqldump -u username -p database_name > backup.sql
```

### 2. تطبيق الإصلاح:
#### عبر phpMyAdmin:
1. ادخل إلى phpMyAdmin
2. اختر قاعدة بيانات WordPress
3. اذهب إلى تبويب "SQL"
4. انسخ محتوى ملف fix_categories.sql
5. <PERSON><PERSON><PERSON><PERSON> "Go"

#### عبر سطر الأوامر:
```bash
mysql -u username -p database_name < fix_categories.sql
```

### 3. التحقق من النتائج:
1. ادخل إلى لوحة تحكم WordPress
2. اذهب إلى Posts > Categories
3. تأكد من وجود التصنيفات الجديدة
4. اذهب إلى Posts > All Posts
5. تحقق من أن المقالات مُصنفة بشكل صحيح

## ما سيحدث:
✅ إنشاء 10 تصنيفات رئيسية (الأبواب)
✅ إنشاء 30 تصنيف فرعي (الفصول)
✅ ربط المقالات بالتصنيفات الصحيحة
✅ إزالة تصنيف "Uncategorized" من المقالات المُصنفة
✅ تحديث عدد المقالات في كل تصنيف

## في حالة المشاكل:
1. استعد النسخة الاحتياطية
2. تحقق من صلاحيات قاعدة البيانات
3. تأكد من دعم utf8mb4

## ملاحظة:
هذا الحل يعمل مع المقالات الموجودة حالياً في قاعدة البيانات.
