/* خارطة كتاب النهضة - تصميم CSS */

.nahda-book-map {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.nahda-book-map h2 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
    font-size: 2.2em;
}

/* إحصائيات الكتاب */
.book-statistics {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.book-statistics h3 {
    margin-top: 0;
    font-size: 1.5em;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stat-number {
    display: block;
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.9;
}

/* البحث السريع */
.quick-search {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
}

.quick-search h3 {
    margin-top: 0;
    color: #495057;
}

#book-search {
    width: 100%;
    padding: 12px 20px;
    border: 2px solid #ced4da;
    border-radius: 25px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

#book-search:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

#search-results {
    margin-top: 15px;
    max-height: 300px;
    overflow-y: auto;
}

/* الخارطة الشجرية */
.tree-map {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.tree-map h3 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 25px;
    font-size: 1.4em;
}

.book-tree {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tree-item {
    margin-bottom: 8px;
}

.item-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.item-header:hover {
    background-color: #f8f9fa;
}

.toggle-btn, .no-toggle {
    width: 20px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 8px;
    color: #6c757d;
}

.toggle-btn:hover {
    color: #3498db;
}

.level-icon {
    margin-left: 8px;
    font-size: 16px;
}

.category-link {
    flex-grow: 1;
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    margin-left: 8px;
}

.category-link:hover {
    color: #3498db;
    text-decoration: underline;
}

.posts-count {
    font-size: 0.85em;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
}

/* مستويات الشجرة */
.level-1 > .item-header {
    font-size: 1.1em;
    font-weight: bold;
    background: #e3f2fd;
    border-right: 4px solid #2196f3;
}

.level-2 > .item-header {
    font-size: 1.05em;
    background: #f3e5f5;
    border-right: 3px solid #9c27b0;
    margin-right: 20px;
}

.level-3 > .item-header {
    background: #e8f5e8;
    border-right: 2px solid #4caf50;
    margin-right: 40px;
}

.level-4 > .item-header {
    background: #fff3e0;
    border-right: 2px solid #ff9800;
    margin-right: 60px;
}

.children {
    list-style: none;
    padding: 0;
    margin: 5px 0 0 20px;
    border-right: 1px dashed #dee2e6;
    padding-right: 15px;
}

/* العرض الشبكي */
.grid-map {
    margin-top: 30px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.category-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-card h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2c3e50;
}

.category-card h4 a {
    text-decoration: none;
    color: inherit;
}

.category-card h4 a:hover {
    color: #3498db;
}

.card-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9em;
    color: #6c757d;
}

.subcategories {
    list-style: none;
    padding: 0;
    margin: 0;
}

.subcategories li {
    padding: 5px 0;
    border-bottom: 1px solid #f8f9fa;
}

.subcategories li:last-child {
    border-bottom: none;
}

.subcategories a {
    text-decoration: none;
    color: #495057;
    font-size: 0.9em;
}

.subcategories a:hover {
    color: #3498db;
}

/* الأكورديون */
.accordion-map {
    margin-top: 30px;
}

.accordion-container {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    overflow: hidden;
}

.accordion-section {
    border-bottom: 1px solid #dee2e6;
}

.accordion-section:last-child {
    border-bottom: none;
}

.accordion-header {
    background: #f8f9fa;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.accordion-header:hover {
    background: #e9ecef;
}

.accordion-header h4 {
    margin: 0;
    color: #2c3e50;
}

.accordion-toggle {
    transition: transform 0.2s ease;
}

.accordion-header.active .accordion-toggle {
    transform: rotate(180deg);
}

.accordion-content {
    padding: 20px;
    display: none;
    background: white;
}

.accordion-content.active {
    display: block;
}

.subcategories-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
    columns: 2;
    column-gap: 30px;
}

.subcategories-list li {
    padding: 5px 0;
    break-inside: avoid;
}

.subcategories-list a {
    text-decoration: none;
    color: #495057;
}

.subcategories-list a:hover {
    color: #3498db;
}

.subcategories-list .count {
    color: #6c757d;
    font-size: 0.85em;
}

.view-category-btn {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9em;
    margin-top: 15px;
    transition: background-color 0.2s ease;
}

.view-category-btn:hover {
    background: #2980b9;
    color: white;
}

/* مسار التنقل */
.nahda-breadcrumb {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    font-size: 0.9em;
}

.nahda-breadcrumb a {
    color: #3498db;
    text-decoration: none;
}

.nahda-breadcrumb a:hover {
    text-decoration: underline;
}

.separator {
    color: #6c757d;
    margin: 0 8px;
}

.breadcrumb-current {
    color: #495057;
    font-weight: 500;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .nahda-book-map {
        padding: 15px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .subcategories-list {
        columns: 1;
    }
    
    .level-2 > .item-header {
        margin-right: 10px;
    }
    
    .level-3 > .item-header {
        margin-right: 20px;
    }
    
    .level-4 > .item-header {
        margin-right: 30px;
    }
}

/* تحسينات إضافية */
.nahda-book-map * {
    box-sizing: border-box;
}

.nahda-book-map .hidden {
    display: none;
}

.nahda-book-map .loading {
    opacity: 0.6;
    pointer-events: none;
}
