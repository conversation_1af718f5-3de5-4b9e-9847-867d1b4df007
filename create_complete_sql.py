#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete SQL Generator for Full Book
Creates comprehensive SQL file with ALL content from the book
"""

from wordpress_sql_generator import WordPressSQLGenerator

def create_complete_book_sql():
    """Create complete SQL file with all book content"""
    print("🚀 إنشاء ملف SQL شامل لكامل الكتاب")
    print("=" * 60)
    
    # Create generator
    generator = WordPressSQLGenerator()
    
    if not generator.structure:
        print("❌ لم يتم العثور على ملف البنية")
        return False
    
    print("📊 معاينة محتوى الكتاب:")
    print(f"   📚 إجمالي الأبواب: {len(generator.structure['parts'])}")
    print(f"   📖 إجمالي الفصول: {len(generator.structure['chapters'])}")
    print(f"   📝 إجمالي المباحث: {len(generator.structure['sections'])}")
    print(f"   📄 إجمالي وحدات المحتوى: {len(generator.structure['content'])}")
    
    # Confirm with user
    print(f"\n⚠️ تحذير: سيتم إنشاء ملف SQL كبير يحتوي على:")
    print(f"   - {len(generator.structure['parts'])} تصنيف رئيسي (الأبواب)")
    print(f"   - {len(generator.structure['chapters'])} تصنيف فرعي (الفصول)")
    print(f"   - {len(generator.structure['content'])} مقال")
    
    response = input(f"\n❓ هل تريد المتابعة؟ (y/n): ").lower().strip()
    
    if response not in ['y', 'yes', 'نعم', 'ن']:
        print("❌ تم إلغاء العملية")
        return False
    
    # Generate complete SQL
    print(f"\n🔧 جاري إنشاء ملف SQL الشامل...")
    
    if generator.generate_complete_sql():
        # Save with descriptive filename
        filename = 'wordpress_complete_book.sql'
        if generator.save_sql_file(filename):
            
            # Create enhanced instructions
            create_complete_instructions(generator.structure)
            
            print(f"\n" + "=" * 60)
            print(f"🎉 تم إنشاء ملف SQL الشامل بنجاح!")
            print(f"📁 الملف: {filename}")
            
            # Show file size
            import os
            file_size = os.path.getsize(filename) / (1024 * 1024)  # MB
            print(f"📊 حجم الملف: {file_size:.2f} MB")
            
            print(f"\n🎯 محتوى الملف:")
            print(f"   ✅ {len(generator.structure['parts'])} باب رئيسي")
            print(f"   ✅ {len(generator.structure['chapters'])} فصل")
            print(f"   ✅ {len(generator.structure['content'])} مقال")
            print(f"   ✅ تصنيفات هرمية كاملة")
            print(f"   ✅ ربط صحيح للمقالات بالتصنيفات")
            
            print(f"\n📋 الملفات المُنشأة:")
            print(f"   - {filename} (ملف SQL الشامل)")
            print(f"   - complete_import_instructions.md (تعليمات مفصلة)")
            
            print(f"\n⚠️ ملاحظات مهمة:")
            print(f"   - الملف كبير، قد يحتاج وقت أطول للاستيراد")
            print(f"   - تأكد من زيادة حدود PHP قبل الاستيراد")
            print(f"   - انشئ نسخة احتياطية قبل التطبيق")
            
            return True
    
    return False

def create_complete_instructions(structure):
    """Create comprehensive import instructions"""
    instructions = f"""
# تعليمات استيراد الكتاب الكامل إلى WordPress

## 📊 محتوى الملف
- **الأبواب**: {len(structure['parts'])} باب رئيسي
- **الفصول**: {len(structure['chapters'])} فصل
- **المقالات**: {len(structure['content'])} مقال
- **الكلمات المقدرة**: {len(structure['content']) * 150:,} كلمة

## ⚠️ متطلبات النظام

### إعدادات PHP المطلوبة:
```ini
max_execution_time = 600
memory_limit = 1024M
post_max_size = 64M
upload_max_filesize = 64M
max_input_vars = 5000
```

### إعدادات MySQL المطلوبة:
```ini
max_allowed_packet = 64M
innodb_buffer_pool_size = 256M
```

## 🚀 خطوات الاستيراد

### الطريقة الأولى: phpMyAdmin (مُوصى بها)

#### 1. تحضير البيئة:
```
- تأكد من إعدادات PHP أعلاه
- انشئ نسخة احتياطية كاملة
- تأكد من مساحة قاعدة البيانات (100MB على الأقل)
```

#### 2. الاستيراد:
```
1. ادخل إلى phpMyAdmin
2. اختر قاعدة بيانات WordPress
3. اذهب إلى "Import"
4. اختر ملف wordpress_complete_book.sql
5. تأكد من Character set: utf8mb4_unicode_ci
6. اضغط "Go"
7. انتظر (قد يستغرق 5-15 دقيقة)
```

### الطريقة الثانية: سطر الأوامر

```bash
# للملفات الكبيرة
mysql -u username -p database_name < wordpress_complete_book.sql

# مع تتبع التقدم
pv wordpress_complete_book.sql | mysql -u username -p database_name
```

### الطريقة الثالثة: تقسيم الملف

إذا كان الملف كبير جداً، يمكن تقسيمه:

```bash
# تقسيم الملف إلى أجزاء أصغر
split -l 1000 wordpress_complete_book.sql part_

# استيراد كل جزء
for file in part_*; do
    mysql -u username -p database_name < $file
done
```

## ✅ التحقق من النجاح

### 1. فحص التصنيفات:
```
- اذهب إلى Posts > Categories
- تأكد من وجود {len(structure['parts'])} تصنيف رئيسي
- تحقق من البنية الهرمية
```

### 2. فحص المقالات:
```
- اذهب إلى Posts > All Posts
- تأكد من وجود {len(structure['content'])} مقال
- تحقق من التصنيفات المُطبقة
```

### 3. فحص المحتوى:
```
- اختبر عرض مقال عشوائي
- تأكد من ظهور النص العربي
- تحقق من التنقل بين التصنيفات
```

## 🔧 استكشاف الأخطاء

### خطأ: "MySQL server has gone away"
**الحل**: زيادة `max_allowed_packet` و `wait_timeout`

### خطأ: "Maximum execution time exceeded"
**الحل**: زيادة `max_execution_time` إلى 600 ثانية

### خطأ: "Memory limit exceeded"
**الحل**: زيادة `memory_limit` إلى 1024M

### خطأ: "Too many connections"
**الحل**: زيادة `max_connections` في MySQL

### خطأ: النص العربي يظهر كرموز
**الحل**: تأكد من `utf8mb4_unicode_ci` في قاعدة البيانات

## 🎯 تحسينات ما بعد الاستيراد

### 1. فهرسة البحث:
```sql
-- إعادة بناء فهارس البحث
OPTIMIZE TABLE wp_posts;
OPTIMIZE TABLE wp_terms;
OPTIMIZE TABLE wp_term_taxonomy;
```

### 2. تحسين الأداء:
```
- تفعيل cache في WordPress
- استخدام CDN للمحتوى الثابت
- تحسين قاعدة البيانات
```

### 3. تحسين SEO:
```
- تثبيت Yoast SEO
- إضافة meta descriptions
- تحسين URLs
- إنشاء sitemap
```

## 📞 الدعم

### في حالة المشاكل:
1. تحقق من سجلات الأخطاء في cPanel/WHM
2. راجع error_log في WordPress
3. تواصل مع مزود الاستضافة للمساعدة
4. استخدم أدوات مراقبة قاعدة البيانات

### نصائح للنجاح:
- اختبر على موقع تجريبي أولاً
- استخدم اتصال إنترنت مستقر
- لا تغلق المتصفح أثناء الاستيراد
- راقب استخدام الموارد أثناء العملية

---

**🎊 مبروك! ستحصل على موقع WordPress كامل بمحتوى كتاب النهضة**

**📧 للدعم الإضافي**: راجع ملفات التعليمات الأخرى في المجلد
"""
    
    with open('complete_import_instructions.md', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ تم إنشاء تعليمات الاستيراد الشاملة: complete_import_instructions.md")

if __name__ == "__main__":
    create_complete_book_sql()
