#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WordPress Content Generator for Arabic Book
Converts Nahda.docx to WordPress-compatible XML format with hierarchical structure
"""

import docx
import re
import json
import xml.etree.ElementTree as ET
from collections import defaultdict, OrderedDict
from datetime import datetime
import html
import uuid
import os

class ArabicBookParser:
    """Parser for Arabic book structure"""

    def __init__(self, file_path):
        self.file_path = file_path
        self.doc = None
        self.structure = {
            'parts': [],  # الأبواب
            'chapters': [],  # الفصول
            'sections': [],  # المباحث
            'content': []  # المحتوى
        }

    def load_document(self):
        """Load the Word document"""
        try:
            self.doc = docx.Document(self.file_path)
            print(f"✅ تم تحميل الوثيقة: {len(self.doc.paragraphs)} فقرة")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل الوثيقة: {e}")
            return False

    def extract_structure(self):
        """Extract hierarchical structure from document"""
        if not self.doc:
            return False

        print("🔍 جاري تحليل بنية الكتاب...")

        current_part = None
        current_chapter = None
        current_section = None
        content_buffer = []

        # Keywords for structure identification
        part_keywords = ['الباب']
        chapter_keywords = ['الفصل']
        section_keywords = ['المبحث', 'المطلب', 'الفرع']
        summary_keywords = ['خلاصة الفصل', 'خاتمة']

        for i, para in enumerate(self.doc.paragraphs):
            text = para.text.strip()
            if not text:
                continue

            # Check for part (الباب)
            if any(keyword in text for keyword in part_keywords) and len(text) < 100:
                if content_buffer and current_chapter:
                    self._save_content(current_part, current_chapter, current_section, content_buffer)
                    content_buffer = []

                current_part = {
                    'id': len(self.structure['parts']) + 1,
                    'title': text,
                    'index': i,
                    'chapters': []
                }
                self.structure['parts'].append(current_part)
                current_chapter = None
                current_section = None
                print(f"📚 تم العثور على باب: {text}")

            # Check for chapter (الفصل)
            elif any(keyword in text for keyword in chapter_keywords) and len(text) < 100:
                if content_buffer and current_chapter:
                    self._save_content(current_part, current_chapter, current_section, content_buffer)
                    content_buffer = []

                current_chapter = {
                    'id': len(self.structure['chapters']) + 1,
                    'title': text,
                    'index': i,
                    'part_id': current_part['id'] if current_part else None,
                    'sections': []
                }
                self.structure['chapters'].append(current_chapter)
                if current_part:
                    current_part['chapters'].append(current_chapter['id'])
                current_section = None
                print(f"📖 تم العثور على فصل: {text}")

            # Check for section (المبحث/المطلب)
            elif any(keyword in text for keyword in section_keywords) and len(text) < 150:
                if content_buffer and current_chapter:
                    self._save_content(current_part, current_chapter, current_section, content_buffer)
                    content_buffer = []

                current_section = {
                    'id': len(self.structure['sections']) + 1,
                    'title': text,
                    'index': i,
                    'chapter_id': current_chapter['id'] if current_chapter else None,
                    'part_id': current_part['id'] if current_part else None
                }
                self.structure['sections'].append(current_section)
                if current_chapter:
                    current_chapter['sections'].append(current_section['id'])
                print(f"📝 تم العثور على مبحث: {text}")

            # Check for summary (خلاصة)
            elif any(keyword in text for keyword in summary_keywords):
                if content_buffer and current_chapter:
                    self._save_content(current_part, current_chapter, current_section, content_buffer)
                    content_buffer = []

                # Treat summary as special content
                content_buffer.append({
                    'type': 'summary',
                    'text': text,
                    'index': i
                })

            else:
                # Regular content
                if len(text) > 50:  # Only substantial content
                    content_buffer.append({
                        'type': 'content',
                        'text': text,
                        'index': i
                    })

        # Save remaining content
        if content_buffer and current_chapter:
            self._save_content(current_part, current_chapter, current_section, content_buffer)

        print(f"✅ تم تحليل البنية:")
        print(f"   - الأبواب: {len(self.structure['parts'])}")
        print(f"   - الفصول: {len(self.structure['chapters'])}")
        print(f"   - المباحث: {len(self.structure['sections'])}")
        print(f"   - وحدات المحتوى: {len(self.structure['content'])}")

        return True

    def _save_content(self, part, chapter, section, content_buffer):
        """Save content buffer as a content unit"""
        if not content_buffer:
            return

        content_unit = {
            'id': len(self.structure['content']) + 1,
            'part_id': part['id'] if part else None,
            'chapter_id': chapter['id'] if chapter else None,
            'section_id': section['id'] if section else None,
            'content': content_buffer.copy(),
            'title': self._generate_content_title(part, chapter, section, content_buffer)
        }

        self.structure['content'].append(content_unit)

    def _generate_content_title(self, part, chapter, section, content_buffer):
        """Generate title for content unit"""
        if section:
            return section['title']
        elif chapter:
            # Use first substantial text as title
            for item in content_buffer:
                if item['type'] == 'content' and len(item['text']) > 20:
                    return item['text'][:100] + "..."
            return f"محتوى من {chapter['title']}"
        elif part:
            return f"محتوى من {part['title']}"
        else:
            return "محتوى عام"

    def save_structure_json(self, output_file='book_structure.json'):
        """Save extracted structure to JSON file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.structure, f, ensure_ascii=False, indent=2)
            print(f"✅ تم حفظ البنية في: {output_file}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ البنية: {e}")
            return False

class WordPressXMLGenerator:
    """Generate WordPress XML from book structure"""

    def __init__(self, structure):
        self.structure = structure
        self.xml_root = None
        self.category_id_counter = 1
        self.post_id_counter = 1

    def generate_xml(self):
        """Generate complete WordPress XML"""
        print("🔧 جاري إنشاء ملف WordPress XML...")

        # Create root RSS element with correct WXR format
        self.xml_root = ET.Element('rss', {
            'version': '2.0',
            'xmlns:excerpt': 'http://wordpress.org/export/1.2/excerpt/',
            'xmlns:content': 'http://purl.org/rss/1.0/modules/content/',
            'xmlns:wfw': 'http://wellformedweb.org/CommentAPI/',
            'xmlns:dc': 'http://purl.org/dc/elements/1.1/',
            'xmlns:wp': 'http://wordpress.org/export/1.2/'
        })

        channel = ET.SubElement(self.xml_root, 'channel')

        # Add channel metadata
        self._add_channel_metadata(channel)

        # Add categories
        self._add_categories(channel)

        # Add posts
        self._add_posts(channel)

        print("✅ تم إنشاء ملف XML بنجاح")
        return True

    def _add_channel_metadata(self, channel):
        """Add WordPress channel metadata"""
        ET.SubElement(channel, 'title').text = 'مشروع النهضة وبناء الدولة السورية'
        ET.SubElement(channel, 'link').text = 'http://localhost'
        ET.SubElement(channel, 'description').text = 'كتاب مشروع النهضة وبناء الدولة السورية - ما بعد الاستبداد'
        ET.SubElement(channel, 'pubDate').text = datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')
        ET.SubElement(channel, 'language').text = 'ar'
        ET.SubElement(channel, 'generator').text = 'https://wordpress.org/?v=6.0'

        # Add WXR version - this is critical for WordPress to recognize the file
        wxr_version = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}wxr_version')
        wxr_version.text = '1.2'

        base_site_url = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}base_site_url')
        base_site_url.text = 'http://localhost'

        base_blog_url = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}base_blog_url')
        base_blog_url.text = 'http://localhost'

    def _add_categories(self, channel):
        """Add hierarchical categories to XML"""
        print("📁 جاري إضافة التصنيفات...")

        # Add main parts as parent categories
        for part in self.structure['parts']:
            cat_elem = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}category')
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}term_id').text = str(self.category_id_counter)
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_nicename').text = f"part-{part['id']}"
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_parent').text = ''
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}cat_name').text = part['title']

            part['wp_category_id'] = self.category_id_counter
            self.category_id_counter += 1

        # Add chapters as sub-categories
        for chapter in self.structure['chapters']:
            cat_elem = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}category')
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}term_id').text = str(self.category_id_counter)
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_nicename').text = f"chapter-{chapter['id']}"

            # Find parent part
            parent_part = next((p for p in self.structure['parts'] if p['id'] == chapter['part_id']), None)
            parent_nicename = f"part-{parent_part['id']}" if parent_part else ''
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_parent').text = parent_nicename
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}cat_name').text = chapter['title']

            chapter['wp_category_id'] = self.category_id_counter
            self.category_id_counter += 1

    def _add_posts(self, channel):
        """Add posts to XML"""
        print("📝 جاري إضافة المقالات...")

        for content_unit in self.structure['content']:
            item = ET.SubElement(channel, 'item')

            # Basic post data
            ET.SubElement(item, 'title').text = content_unit['title']
            ET.SubElement(item, 'link').text = f"http://localhost/?p={self.post_id_counter}"
            ET.SubElement(item, 'pubDate').text = datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')
            ET.SubElement(item, '{http://purl.org/dc/elements/1.1/}creator').text = 'admin'
            ET.SubElement(item, 'guid', {'isPermaLink': 'false'}).text = f"http://localhost/?p={self.post_id_counter}"
            ET.SubElement(item, 'description').text = ''

            # Post content
            content_text = self._build_post_content(content_unit)
            ET.SubElement(item, '{http://purl.org/rss/1.0/modules/content/}encoded').text = content_text

            # WordPress specific data
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_id').text = str(self.post_id_counter)
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_date').text = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_date_gmt').text = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}comment_status').text = 'open'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}ping_status').text = 'open'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_name').text = f"post-{self.post_id_counter}"
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}status').text = 'publish'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_parent').text = '0'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}menu_order').text = '0'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_type').text = 'post'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_password').text = ''
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}is_sticky').text = '0'

            # Add categories
            self._add_post_categories(item, content_unit)

            self.post_id_counter += 1

    def _build_post_content(self, content_unit):
        """Build HTML content for post"""
        html_content = []

        for item in content_unit['content']:
            if item['type'] == 'content':
                html_content.append(f"<p>{html.escape(item['text'])}</p>")
            elif item['type'] == 'summary':
                html_content.append(f"<div class='chapter-summary'><h3>خلاصة</h3><p>{html.escape(item['text'])}</p></div>")

        return '\n'.join(html_content)

    def _add_post_categories(self, item, content_unit):
        """Add category assignments to post"""
        # Add part category if exists
        if content_unit.get('part_id'):
            part = next((p for p in self.structure['parts'] if p['id'] == content_unit['part_id']), None)
            if part and 'wp_category_id' in part:
                category = ET.SubElement(item, 'category', {
                    'domain': 'category',
                    'nicename': f"part-{part['id']}"
                })
                category.text = part['title']

        # Add chapter category if exists
        if content_unit.get('chapter_id'):
            chapter = next((c for c in self.structure['chapters'] if c['id'] == content_unit['chapter_id']), None)
            if chapter and 'wp_category_id' in chapter:
                category = ET.SubElement(item, 'category', {
                    'domain': 'category',
                    'nicename': f"chapter-{chapter['id']}"
                })
                category.text = chapter['title']

    def save_xml(self, output_file='wordpress_import.xml'):
        """Save XML to file"""
        try:
            # Create pretty XML
            self._indent(self.xml_root)
            tree = ET.ElementTree(self.xml_root)

            with open(output_file, 'wb') as f:
                tree.write(f, encoding='utf-8', xml_declaration=True)

            print(f"✅ تم حفظ ملف WordPress XML في: {output_file}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ ملف XML: {e}")
            return False

    def _indent(self, elem, level=0):
        """Add indentation to XML for readability"""
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                self._indent(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i

def main():
    """Main execution function"""
    print("🚀 بدء تحويل الكتاب إلى تنسيق WordPress")
    print("=" * 50)

    # Step 1: Parse document
    parser = ArabicBookParser('Nahda.docx')

    if not parser.load_document():
        return False

    if not parser.extract_structure():
        return False

    # Save structure for reference
    parser.save_structure_json()

    # Step 2: Generate WordPress XML
    generator = WordPressXMLGenerator(parser.structure)

    if not generator.generate_xml():
        return False

    if not generator.save_xml():
        return False

    print("\n" + "=" * 50)
    print("✅ تم إكمال التحويل بنجاح!")
    print("📁 الملفات المُنشأة:")
    print("   - book_structure.json (بنية الكتاب)")
    print("   - wordpress_import.xml (ملف الاستيراد)")
    print("\n📋 الخطوات التالية:")
    print("   1. ادخل إلى لوحة تحكم WordPress")
    print("   2. اذهب إلى Tools > Import")
    print("   3. اختر WordPress واستورد ملف wordpress_import.xml")
    print("   4. تأكد من تفعيل دعم اللغة العربية في الموقع")

    return True

if __name__ == "__main__":
    main()
