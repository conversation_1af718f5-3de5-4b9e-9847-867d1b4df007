
# تعليمات استيراد الملفات المتعددة

## الملفات المُنشأة:
- 01_categories.sql - التصنيفات (528 تصنيف)
- 02_posts.sql إلى XX_posts.sql - المقالات (500 مقال لكل ملف)
- 99_setup.sql - الإعدادات النهائية

## خطوات الاستيراد:

### 1. استيراد التصنيفات:
```bash
mysql -u username -p database_name < 01_categories.sql
```

### 2. استيراد المقالات (بالترتيب):
```bash
mysql -u username -p database_name < 02_posts.sql
mysql -u username -p database_name < 03_posts.sql
# ... وهكذا لجميع ملفات المقالات
```

### 3. تطبيق الإعدادات النهائية:
```bash
mysql -u username -p database_name < 99_setup.sql
```

## استيراد تلقائي (Linux/Mac):
```bash
for file in *.sql; do
    echo "Importing $file..."
    mysql -u username -p database_name < "$file"
done
```

## استيراد تلقائي (Windows):
```cmd
for %f in (*.sql) do mysql -u username -p database_name < "%f"
```

## المميزات:
✅ استيراد تدريجي آمن
✅ إمكانية إيقاف واستكمال
✅ ملفات أصغر وأسرع
✅ أقل استهلاك للذاكرة
