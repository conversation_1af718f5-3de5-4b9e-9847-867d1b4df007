#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Categories SQL Generator
Creates SQL to fix existing posts categories in WordPress database
"""

import json
import re

class CategoriesFixGenerator:
    """Generate SQL to fix categories for existing posts"""
    
    def __init__(self, structure_file='book_structure.json', table_prefix='wp_'):
        self.structure = None
        self.table_prefix = table_prefix
        self.sql_statements = []
        self.load_structure(structure_file)
        
    def load_structure(self, file_path):
        """Load structure from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.structure = json.load(f)
            print(f"✅ تم تحميل البنية من: {file_path}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False
    
    def escape_sql(self, text):
        """Escape text for SQL"""
        if not text:
            return ''
        return text.replace("'", "\\'").replace('"', '\\"')
    
    def generate_slug(self, text):
        """Generate URL-friendly slug"""
        text = re.sub(r'[^\w\s-]', '', text)
        text = re.sub(r'[-\s]+', '-', text)
        return text.strip('-').lower()[:50]
    
    def generate_fix_sql(self):
        """Generate SQL to fix categories"""
        print("🔧 جاري إنشاء SQL لإصلاح التصنيفات...")
        
        # Add header
        self.sql_statements.append("-- Fix Categories SQL for WordPress")
        self.sql_statements.append("-- This will create proper categories and assign posts to them")
        self.sql_statements.append("SET NAMES utf8mb4;")
        self.sql_statements.append("")
        
        # Step 1: Create categories if they don't exist
        self.sql_statements.append("-- إنشاء التصنيفات الجديدة")
        
        category_id = 100  # Start from 100 to avoid conflicts
        term_taxonomy_id = 100
        
        # Create parent categories (parts)
        for i, part in enumerate(self.structure['parts'][:10]):
            slug = self.generate_slug(part['title']) or f"part-{part['id']}"
            name = self.escape_sql(part['title'][:100])
            
            self.sql_statements.append(f"""
-- إنشاء الباب: {part['title'][:50]}...
INSERT IGNORE INTO {self.table_prefix}terms (term_id, name, slug, term_group) 
VALUES ({category_id}, '{name}', '{slug}', 0);

INSERT IGNORE INTO {self.table_prefix}term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES ({term_taxonomy_id}, {category_id}, 'category', '', 0, 0);""")
            
            part['wp_term_id'] = category_id
            part['wp_term_taxonomy_id'] = term_taxonomy_id
            
            category_id += 1
            term_taxonomy_id += 1
        
        # Create child categories (chapters)
        for chapter in self.structure['chapters'][:30]:
            if chapter.get('part_id') and chapter['part_id'] <= 10:
                slug = self.generate_slug(chapter['title']) or f"chapter-{chapter['id']}"
                name = self.escape_sql(chapter['title'][:100])
                
                # Find parent
                parent_part = next((p for p in self.structure['parts'] if p['id'] == chapter['part_id']), None)
                parent_id = parent_part['wp_term_taxonomy_id'] if parent_part else 0
                
                self.sql_statements.append(f"""
-- إنشاء الفصل: {chapter['title'][:50]}...
INSERT IGNORE INTO {self.table_prefix}terms (term_id, name, slug, term_group) 
VALUES ({category_id}, '{name}', '{slug}', 0);

INSERT IGNORE INTO {self.table_prefix}term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES ({term_taxonomy_id}, {category_id}, 'category', '', {parent_id}, 0);""")
                
                chapter['wp_term_id'] = category_id
                chapter['wp_term_taxonomy_id'] = term_taxonomy_id
                
                category_id += 1
                term_taxonomy_id += 1
        
        # Step 2: Remove existing wrong category assignments
        self.sql_statements.append("\n-- إزالة التصنيفات الخاطئة من المقالات")
        self.sql_statements.append(f"""
DELETE FROM {self.table_prefix}term_relationships 
WHERE object_id IN (
    SELECT ID FROM {self.table_prefix}posts 
    WHERE post_type = 'post' 
    AND post_status = 'publish'
    AND post_title LIKE '%الباب%' 
    OR post_title LIKE '%الفصل%'
    OR post_title LIKE '%المبحث%'
);""")
        
        # Step 3: Assign correct categories to posts
        self.sql_statements.append("\n-- تعيين التصنيفات الصحيحة للمقالات")
        
        post_counter = 1
        for content_unit in self.structure['content'][:50]:
            if content_unit.get('chapter_id'):
                chapter = next((c for c in self.structure['chapters'] 
                              if c['id'] == content_unit['chapter_id'] and 'wp_term_taxonomy_id' in c), None)
                if chapter:
                    # Find post by title pattern or order
                    title_pattern = self.escape_sql(content_unit['title'][:50])
                    
                    self.sql_statements.append(f"""
-- تعيين تصنيف للمقال رقم {post_counter}
INSERT IGNORE INTO {self.table_prefix}term_relationships (object_id, term_taxonomy_id, term_order)
SELECT p.ID, {chapter['wp_term_taxonomy_id']}, 0
FROM {self.table_prefix}posts p
WHERE p.post_type = 'post' 
AND p.post_status = 'publish'
AND (p.post_title LIKE '{title_pattern}%' OR p.ID = {post_counter})
LIMIT 1;""")
            
            post_counter += 1
        
        # Step 4: Update category counts
        self.sql_statements.append("\n-- تحديث عدد المقالات في كل تصنيف")
        self.sql_statements.append(f"""
UPDATE {self.table_prefix}term_taxonomy tt
SET count = (
    SELECT COUNT(*) 
    FROM {self.table_prefix}term_relationships tr 
    WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
)
WHERE tt.taxonomy = 'category';""")
        
        # Step 5: Remove uncategorized from posts that now have categories
        self.sql_statements.append("\n-- إزالة تصنيف 'غير مصنف' من المقالات التي لها تصنيفات أخرى")
        self.sql_statements.append(f"""
DELETE tr1 FROM {self.table_prefix}term_relationships tr1
INNER JOIN {self.table_prefix}term_taxonomy tt1 ON tr1.term_taxonomy_id = tt1.term_taxonomy_id
INNER JOIN {self.table_prefix}terms t1 ON tt1.term_id = t1.term_id
WHERE t1.slug = 'uncategorized'
AND tr1.object_id IN (
    SELECT DISTINCT tr2.object_id 
    FROM {self.table_prefix}term_relationships tr2
    INNER JOIN {self.table_prefix}term_taxonomy tt2 ON tr2.term_taxonomy_id = tt2.term_taxonomy_id
    INNER JOIN {self.table_prefix}terms t2 ON tt2.term_id = t2.term_id
    WHERE t2.slug != 'uncategorized'
);""")
        
        print("✅ تم إنشاء SQL لإصلاح التصنيفات")
        return True
    
    def save_fix_sql(self, filename='fix_categories.sql'):
        """Save fix SQL to file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.sql_statements))
            
            print(f"✅ تم حفظ ملف إصلاح التصنيفات: {filename}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ ملف SQL: {e}")
            return False
    
    def create_fix_instructions(self):
        """Create instructions for fixing categories"""
        instructions = """
# تعليمات إصلاح التصنيفات في WordPress

## المشكلة:
جميع المقالات تظهر في تصنيف "Uncategorized" بدلاً من التصنيفات الصحيحة.

## الحل:
استخدام ملف SQL لإصلاح التصنيفات مباشرة في قاعدة البيانات.

## خطوات التطبيق:

### 1. إنشاء نسخة احتياطية:
```sql
-- في phpMyAdmin أو سطر الأوامر
mysqldump -u username -p database_name > backup.sql
```

### 2. تطبيق الإصلاح:
#### عبر phpMyAdmin:
1. ادخل إلى phpMyAdmin
2. اختر قاعدة بيانات WordPress
3. اذهب إلى تبويب "SQL"
4. انسخ محتوى ملف fix_categories.sql
5. اضغط "Go"

#### عبر سطر الأوامر:
```bash
mysql -u username -p database_name < fix_categories.sql
```

### 3. التحقق من النتائج:
1. ادخل إلى لوحة تحكم WordPress
2. اذهب إلى Posts > Categories
3. تأكد من وجود التصنيفات الجديدة
4. اذهب إلى Posts > All Posts
5. تحقق من أن المقالات مُصنفة بشكل صحيح

## ما سيحدث:
✅ إنشاء 10 تصنيفات رئيسية (الأبواب)
✅ إنشاء 30 تصنيف فرعي (الفصول)
✅ ربط المقالات بالتصنيفات الصحيحة
✅ إزالة تصنيف "Uncategorized" من المقالات المُصنفة
✅ تحديث عدد المقالات في كل تصنيف

## في حالة المشاكل:
1. استعد النسخة الاحتياطية
2. تحقق من صلاحيات قاعدة البيانات
3. تأكد من دعم utf8mb4

## ملاحظة:
هذا الحل يعمل مع المقالات الموجودة حالياً في قاعدة البيانات.
"""
        
        with open('fix_categories_instructions.md', 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print("✅ تم إنشاء تعليمات الإصلاح: fix_categories_instructions.md")

def main():
    """Main function"""
    print("🔧 مولد SQL لإصلاح التصنيفات في WordPress")
    print("=" * 50)
    
    generator = CategoriesFixGenerator()
    
    if not generator.structure:
        print("❌ لم يتم العثور على ملف البنية")
        return False
    
    if generator.generate_fix_sql():
        if generator.save_fix_sql():
            generator.create_fix_instructions()
            
            print("\n" + "=" * 50)
            print("✅ تم إنشاء ملف إصلاح التصنيفات!")
            print("📁 الملفات المُنشأة:")
            print("   - fix_categories.sql (ملف SQL للإصلاح)")
            print("   - fix_categories_instructions.md (تعليمات التطبيق)")
            
            print("\n🎯 هذا الملف سيقوم بـ:")
            print("   ✅ إنشاء التصنيفات الصحيحة")
            print("   ✅ ربط المقالات الموجودة بالتصنيفات")
            print("   ✅ إزالة تصنيف 'Uncategorized'")
            print("   ✅ تحديث عدد المقالات")
            
            print("\n⚠️ تذكير:")
            print("   - انشئ نسخة احتياطية قبل التطبيق")
            print("   - اختبر على قاعدة بيانات تجريبية أولاً")
            
            return True
    
    return False

if __name__ == "__main__":
    main()
