# زيادة حدود الرفع في WordPress

## 🚨 المشكلة الحالية
```
This does not appear to be a WXR file, missing/invalid WXR version number
```

هذه المشكلة تحدث لسببين:
1. **تنسيق WXR غير صحيح** - تم إصلاحه في `fix_wordpress_xml.py`
2. **حجم الملف كبير جداً** - يحتاج زيادة حدود الرفع

## 🔧 الحلول

### الحل 1: استخدام الملفات المقسمة (الأسهل)

```bash
python fix_wordpress_xml.py
```

هذا سينشئ ملفات أصغر:
- `wordpress_categories.xml` - التصنيفات فقط
- `wordpress_posts_1_to_50.xml` - أول 50 مقال
- `wordpress_posts_51_to_100.xml` - المقالات 51-100
- وهكذا...

### الحل 2: زيادة حدود الرفع في WordPress

#### أ) عبر ملف .htaccess (الأسهل)
أضف هذه الأسطر إلى ملف `.htaccess` في جذر WordPress:

```apache
# زيادة حدود الرفع
php_value upload_max_filesize 64M
php_value post_max_size 64M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 512M
```

#### ب) عبر ملف wp-config.php
أضف هذه الأسطر إلى `wp-config.php` قبل `/* That's all, stop editing! */`:

```php
// زيادة حدود الرفع
@ini_set('upload_max_filesize', '64M');
@ini_set('post_max_size', '64M');
@ini_set('max_execution_time', 300);
@ini_set('memory_limit', '512M');
```

#### ج) عبر ملف php.ini (الأفضل)
إذا كان لديك وصول إلى ملف `php.ini`:

```ini
upload_max_filesize = 64M
post_max_size = 64M
max_execution_time = 300
max_input_time = 300
memory_limit = 512M
max_input_vars = 3000
```

#### د) عبر functions.php (للقوالب)
أضف إلى ملف `functions.php` في القالب:

```php
// زيادة حدود الرفع
function increase_upload_limits() {
    @ini_set('upload_max_filesize', '64M');
    @ini_set('post_max_size', '64M');
    @ini_set('max_execution_time', 300);
    @ini_set('memory_limit', '512M');
}
add_action('init', 'increase_upload_limits');
```

## 📋 خطوات الاستيراد المُحسنة

### الطريقة الأولى: الملفات المقسمة (مُوصى بها)

1. **تشغيل المُصحح**:
   ```bash
   python fix_wordpress_xml.py
   ```

2. **استيراد التصنيفات أولاً**:
   - Tools > Import > WordPress
   - ارفع `wordpress_categories.xml`
   - أكمل الاستيراد

3. **استيراد المقالات تدريجياً**:
   - ارفع `wordpress_posts_1_to_50.xml`
   - انتظر حتى ينتهي
   - ارفع `wordpress_posts_51_to_100.xml`
   - وهكذا...

### الطريقة الثانية: الملف الكامل

1. **زيادة الحدود** (اختر إحدى الطرق أعلاه)

2. **إعادة تشغيل الخادم** (إذا لزم الأمر)

3. **تشغيل المُصحح للملف الكامل**:
   ```bash
   python wordpress_converter.py
   ```

4. **الاستيراد**:
   - Tools > Import > WordPress
   - ارفع `wordpress_import.xml`

## 🔍 التحقق من الحدود الحالية

أنشئ ملف PHP مؤقت للتحقق من الحدود:

```php
<?php
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Input Vars: " . ini_get('max_input_vars') . "<br>";
?>
```

## ⚠️ استكشاف الأخطاء

### خطأ: "This does not appear to be a WXR file"
**الحل**: استخدم `fix_wordpress_xml.py` لإنشاء ملف WXR صحيح

### خطأ: "File is too large"
**الحل**: زيادة `upload_max_filesize` و `post_max_size`

### خطأ: "Maximum execution time exceeded"
**الحل**: زيادة `max_execution_time` إلى 300 أو أكثر

### خطأ: "Fatal error: Allowed memory size exhausted"
**الحل**: زيادة `memory_limit` إلى 512M أو أكثر

### خطأ: "The uploaded file exceeds the upload_max_filesize directive"
**الحل**: تحقق من أن التغييرات تم تطبيقها بشكل صحيح

## 🎯 التوصيات

### للمواقع الصغيرة:
- استخدم الملفات المقسمة
- ابدأ بـ 50 مقال في كل ملف

### للمواقع الكبيرة:
- زيادة حدود PHP
- استخدم الملف الكامل
- تأكد من وجود نسخة احتياطية

### للاستضافة المشتركة:
- استخدم `.htaccess` أو `wp-config.php`
- إذا لم تعمل، استخدم الملفات المقسمة

### للخوادم المخصصة:
- عدّل `php.ini` مباشرة
- أعد تشغيل الخادم
- استخدم الملف الكامل

## 📞 الدعم الإضافي

إذا استمرت المشاكل:
1. تحقق من سجلات الأخطاء في WordPress
2. تواصل مع مزود الاستضافة
3. استخدم إضافة لزيادة الحدود مثل "Increase Upload Max Filesize"
