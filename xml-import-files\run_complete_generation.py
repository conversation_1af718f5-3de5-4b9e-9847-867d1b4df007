#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل شامل لإنشاء ملفات استيراد كتاب النهضة
يقوم بتحليل الكتاب وإنشاء جميع ملفات XML المطلوبة
"""

import os
import sys
import subprocess
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("🚀 مولد ملفات استيراد كتاب النهضة السورية")
    print("=" * 60)
    print("📖 كتاب: مشروع النهضة وبناء الدولة السورية")
    print("🎯 الهدف: إنشاء ملفات XML للاستيراد في WordPress")
    print("⏰ الوقت:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)

def check_requirements():
    """فحص المتطلبات"""
    print("\n🔍 فحص المتطلبات...")

    # فحص Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")

    # فحص المكتبات المطلوبة
    required_modules = ['docx', 'json', 're', 'os', 'datetime']
    missing_modules = []

    for module in required_modules:
        try:
            if module == 'docx':
                import docx
            else:
                __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module}")

    if missing_modules:
        print(f"\n❌ مكتبات مفقودة: {', '.join(missing_modules)}")
        if 'docx' in missing_modules:
            print("💡 لتثبيت python-docx: pip install python-docx")
        return False

    # فحص ملف الكتاب
    book_file = '../Nahda.docx'
    if not os.path.exists(book_file):
        print(f"❌ ملف الكتاب غير موجود: {book_file}")
        return False
    print(f"✅ ملف الكتاب موجود: {book_file}")

    return True

def run_analysis():
    """تشغيل تحليل الكتاب"""
    print("\n📊 المرحلة 1: تحليل الكتاب...")
    print("-" * 40)

    try:
        # استيراد وتشغيل محلل الكتاب
        from create_import_files import NahdaXMLGenerator

        generator = NahdaXMLGenerator()

        # تحليل الكتاب
        if not generator.analyze_book('../Nahda.docx'):
            print("❌ فشل في تحليل الكتاب")
            return False

        # إنشاء بنية WordPress
        generator.create_wordpress_structure()

        # حفظ البنية
        generator.save_structure()

        print("✅ تم تحليل الكتاب بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحليل الكتاب: {e}")
        return False

def run_xml_generation():
    """تشغيل إنشاء ملفات XML"""
    print("\n🔧 المرحلة 2: إنشاء ملفات XML...")
    print("-" * 40)

    try:
        # استيراد وتشغيل مولد XML
        from generate_xml_files import WordPressXMLGenerator

        generator = WordPressXMLGenerator()

        # تحميل البنية
        if not generator.load_structure():
            print("❌ فشل في تحميل البنية")
            return False

        # إنشاء ملف الاختبار
        print("🧪 إنشاء ملف الاختبار...")
        generator.create_test_file()

        # إنشاء ملف التصنيفات
        print("📂 إنشاء ملف التصنيفات...")
        generator.create_categories_file()

        # إنشاء ملفات المقالات
        print("📄 إنشاء ملفات المقالات...")
        posts_files_count = generator.create_posts_files(9)

        # إنشاء دليل الاستيراد
        print("📋 إنشاء دليل الاستيراد...")
        generator.create_import_guide(posts_files_count)

        print("✅ تم إنشاء ملفات XML بنجاح")
        return True, generator

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملفات XML: {e}")
        return False, None

def create_readme():
    """إنشاء ملف README للمجلد"""
    readme_content = f"""# 📁 ملفات XML لاستيراد كتاب النهضة

## 🎯 نظرة عامة

هذا المجلد يحتوي على ملفات XML جاهزة لاستيراد كتاب "مشروع النهضة وبناء الدولة السورية" إلى WordPress.

## 📋 الملفات المُنشأة

### 🧪 **ملف الاختبار:**
- `test_hierarchical.xml` - اختبار البنية الهرمية (20 تصنيف + 15 مقال)

### 📂 **ملف التصنيفات:**
- `hierarchical_categories.xml` - جميع التصنيفات الهرمية

### 📄 **ملفات المقالات:**
- `posts_part_01.xml` إلى `posts_part_09.xml` - المقالات مقسمة

### 📚 **ملفات الدعم:**
- `IMPORT_GUIDE.md` - دليل الاستيراد التفصيلي
- `book_structure_detailed.json` - البنية الأصلية للكتاب
- `wordpress_structure_ready.json` - بنية WordPress الجاهزة

## 🚀 البدء السريع

### 1️⃣ **اختبار البنية (5 دقائق):**
```
أدوات → استيراد → WordPress → test_hierarchical.xml
```

### 2️⃣ **استيراد التصنيفات (10 دقائق):**
```
أدوات → استيراد → WordPress → hierarchical_categories.xml
```

### 3️⃣ **استيراد المقالات (45-75 دقيقة):**
```
أدوات → استيراد → WordPress → posts_part_01.xml
أدوات → استيراد → WordPress → posts_part_02.xml
... (استمر بالترتيب)
```

## 📊 البنية الهرمية

```
📚 القسم الأول
├── 📖 الباب الأول
│   ├── 📝 الفصل الأول
│   │   ├── 🔸 المبحث الأول
│   │   └── 🔸 المبحث الثاني
│   └── 📝 الفصل الثاني
└── 📖 الباب الثاني
```

## ⚠️ تعليمات مهمة

1. **استورد الملفات بالترتيب المحدد**
2. **انتظر اكتمال كل ملف** قبل الانتقال للتالي
3. **احتفظ بنسخة احتياطية** من موقعك قبل البدء

## 📞 الدعم

للمساعدة راجع:
- `IMPORT_GUIDE.md` - دليل مفصل
- `../documentation/` - أدلة شاملة

---

**🎉 تم إنشاء الملفات في:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("✅ تم إنشاء: README.md")

def display_final_summary(generator):
    """عرض الملخص النهائي"""
    print("\n" + "=" * 60)
    print("🎉 تم إنجاز إنشاء ملفات الاستيراد بنجاح!")
    print("=" * 60)

    # إحصائيات الملفات
    xml_files = [f for f in os.listdir('.') if f.endswith('.xml')]
    total_size = 0

    print(f"\n📁 الملفات المُنشأة ({len(xml_files)} ملف XML):")
    for xml_file in sorted(xml_files):
        size = os.path.getsize(xml_file) / 1024
        total_size += size
        print(f"   📄 {xml_file} ({size:.1f} KB)")

    # إحصائيات المحتوى
    if generator and generator.wp_structure:
        categories_count = len(generator.wp_structure['categories'])
        posts_count = len(generator.wp_structure['posts'])

        print(f"\n📊 إحصائيات المحتوى:")
        print(f"   📂 التصنيفات: {categories_count}")
        print(f"   📄 المقالات: {posts_count}")
        print(f"   💾 إجمالي الحجم: {total_size:.1f} KB ({total_size/1024:.1f} MB)")

    # الخطوات التالية
    print(f"\n🎯 الخطوات التالية:")
    print(f"   1️⃣ اقرأ IMPORT_GUIDE.md للتعليمات التفصيلية")
    print(f"   2️⃣ ابدأ بـ test_hierarchical.xml للاختبار")
    print(f"   3️⃣ استورد hierarchical_categories.xml للتصنيفات")
    print(f"   4️⃣ استورد ملفات posts_part بالترتيب")
    print(f"   5️⃣ تحقق من النتائج في لوحة تحكم WordPress")

    # تقدير الوقت
    estimated_time = 15 + (len([f for f in xml_files if f.startswith('posts_part')]) * 7)
    print(f"\n⏰ الوقت المتوقع للاستيراد: {estimated_time}-{estimated_time + 30} دقيقة")

    print(f"\n🎉 موفق في إنشاء موقع WordPress شامل لكتاب النهضة!")

def main():
    """الدالة الرئيسية"""
    print_header()

    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        return False

    # تحليل الكتاب
    if not run_analysis():
        print("\n❌ فشل في تحليل الكتاب")
        return False

    # إنشاء ملفات XML
    success, generator = run_xml_generation()
    if not success:
        print("\n❌ فشل في إنشاء ملفات XML")
        return False

    # إنشاء README
    create_readme()

    # عرض الملخص النهائي
    display_final_summary(generator)

    return True

if __name__ == "__main__":
    # تغيير المجلد إلى مجلد الملف
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    try:
        success = main()
        if success:
            print(f"\n✅ تم الانتهاء بنجاح!")
        else:
            print(f"\n❌ فشل في العملية!")
            sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n⚠️ تم إيقاف العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
