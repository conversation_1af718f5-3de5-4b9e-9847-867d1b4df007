#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد شامل للنظام المنظم - كتاب النهضة
تشغيل كامل لإنشاء النظام المنظم: مقدمة → قسم → باب → فصل (مقال كامل)
"""

import os
import sys
import subprocess
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("🚀 المولد الشامل للنظام المنظم - كتاب النهضة")
    print("=" * 70)
    print("📋 النظام: مقدمة → قسم → باب → فصل (مقال كامل)")
    print("🎨 التنسيق: HTML جميل ومقروء")
    print("🎯 الهدف: كل فصل في مقال كامل مع ترتيب وتناسق")
    print("=" * 70)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")

    # فحص ملف الكتاب
    if not os.path.exists('../Nahda.docx'):
        print("❌ لم يتم العثور على ملف الكتاب: ../Nahda.docx")
        return False

    # فحص مكتبة python-docx
    try:
        import docx
        print("✅ مكتبة python-docx متوفرة")
    except ImportError:
        print("❌ مكتبة python-docx غير متوفرة")
        print("💡 قم بتثبيتها: pip install python-docx")
        return False

    print("✅ جميع المتطلبات متوفرة")
    return True

def run_organized_analysis():
    """تشغيل تحليل الكتاب المنظم"""
    print("\n📖 المرحلة 1: تحليل الكتاب وإنشاء البنية المنظمة")
    print("-" * 50)

    try:
        # تعيين متغيرات البيئة للترميز
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        result = subprocess.run([sys.executable, 'create_organized_book.py'],
                              capture_output=True, text=True, encoding='utf-8', env=env)

        if result.returncode == 0:
            print("✅ تم تحليل الكتاب وإنشاء البنية المنظمة بنجاح")

            # طباعة النتائج
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if any(keyword in line for keyword in ['📚', '📖', '📄', '📊', '💾']):
                    print(f"   {line}")

            return True
        else:
            print("❌ فشل في تحليل الكتاب")
            print(f"خطأ: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ خطأ في تشغيل تحليل الكتاب: {e}")
        return False

def run_organized_xml_generation():
    """تشغيل إنشاء ملفات XML المنظمة"""
    print("\n🔧 المرحلة 2: إنشاء ملفات XML للنظام المنظم")
    print("-" * 50)

    try:
        # تعيين متغيرات البيئة للترميز
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        result = subprocess.run([sys.executable, 'generate_organized_xml.py'],
                              capture_output=True, text=True, encoding='utf-8', env=env)

        if result.returncode == 0:
            print("✅ تم إنشاء ملفات XML المنظمة بنجاح")

            # طباعة النتائج
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if any(keyword in line for keyword in ['✅', '📂', '📄', '🧪']):
                    print(f"   {line}")

            return True
        else:
            print("❌ فشل في إنشاء ملفات XML")
            print(f"خطأ: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ خطأ في تشغيل إنشاء XML: {e}")
        return False

def list_generated_files():
    """عرض الملفات المُنشأة"""
    print("\n📁 الملفات المُنشأة:")
    print("-" * 30)

    files_info = [
        ('test_organized_book.xml', 'ملف اختبار النظام المنظم'),
        ('organized_categories.xml', 'جميع التصنيفات الهرمية'),
        ('organized_posts_part_01.xml', 'مجموعة المقالات الأولى'),
        ('organized_posts_part_02.xml', 'مجموعة المقالات الثانية'),
        ('organized_posts_part_03.xml', 'مجموعة المقالات الثالثة'),
        ('ORGANIZED_IMPORT_GUIDE.md', 'دليل الاستيراد المنظم'),
        ('book_structure_organized.json', 'البنية الكاملة للكتاب'),
        ('ORGANIZED_BOOK_FINAL_REPORT.md', 'التقرير النهائي الشامل')
    ]

    total_size = 0
    files_found = 0

    for filename, description in files_info:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename) / 1024  # KB
            total_size += file_size
            files_found += 1

            if filename.endswith('.xml'):
                print(f"📄 {filename:<30} ({file_size:>6.1f} KB) - {description}")
            elif filename.endswith('.md'):
                print(f"📚 {filename:<30} ({file_size:>6.1f} KB) - {description}")
            else:
                print(f"📊 {filename:<30} ({file_size:>6.1f} KB) - {description}")
        else:
            print(f"❌ {filename:<30} (غير موجود) - {description}")

    print(f"\n📊 الإجمالي: {files_found} ملف ({total_size:.1f} KB)")

def print_import_instructions():
    """طباعة تعليمات الاستيراد"""
    print("\n🚀 تعليمات الاستيراد:")
    print("=" * 40)

    print("\n1️⃣ اختبار النظام (5 دقائق):")
    print("   أدوات → استيراد → WordPress → test_organized_book.xml")

    print("\n2️⃣ استيراد التصنيفات (10 دقائق):")
    print("   أدوات → استيراد → WordPress → organized_categories.xml")

    print("\n3️⃣ استيراد المقالات (45-75 دقيقة):")
    print("   أدوات → استيراد → WordPress → organized_posts_part_01.xml")
    print("   أدوات → استيراد → WordPress → organized_posts_part_02.xml")
    print("   أدوات → استيراد → WordPress → organized_posts_part_03.xml")

    print("\n⏰ الوقت الإجمالي: 60-90 دقيقة")

def print_final_summary():
    """طباعة الملخص النهائي"""
    print("\n🎉 تم إنجاز النظام المنظم بنجاح!")
    print("=" * 50)

    # قراءة إحصائيات من ملف البنية إن وجد
    try:
        import json
        with open('book_structure_organized.json', 'r', encoding='utf-8') as f:
            structure = json.load(f)

        parts_count = len(structure['parts'])
        chapters_count = len(structure['chapters'])
        sections_count = len(structure['sections'])

        total_words = 0
        if structure['introduction']:
            total_words += structure['introduction']['word_count']

        for section in structure['sections']:
            total_words += section['word_count']

        print(f"📊 إحصائيات النظام المنظم:")
        print(f"   📖 المقدمة: {'موجودة' if structure['introduction'] else 'غير موجودة'}")
        print(f"   📚 الأقسام: {parts_count}")
        print(f"   📖 الأبواب: {chapters_count}")
        print(f"   📄 الفصول (المقالات): {sections_count}")
        print(f"   📚 إجمالي الكلمات: {total_words:,}")
        print(f"   📈 متوسط الكلمات/مقال: {total_words // (sections_count + 1) if sections_count > 0 else 0}")

    except Exception as e:
        print(f"📊 إحصائيات النظام المنظم: (غير متوفرة - {e})")

    print(f"\n🎯 المميزات المحققة:")
    print(f"   ✅ كل فصل في مقال كامل")
    print(f"   ✅ ترتيب وتناسق مثالي")
    print(f"   ✅ تنسيق HTML بصري جميل")
    print(f"   ✅ بنية هرمية منطقية")
    print(f"   ✅ كل كلمة محفوظة ومنظمة")

    print(f"\n📚 للمزيد من المعلومات:")
    print(f"   📖 اقرأ: ORGANIZED_IMPORT_GUIDE.md")
    print(f"   📊 راجع: ORGANIZED_BOOK_FINAL_REPORT.md")

def main():
    """الدالة الرئيسية"""
    start_time = datetime.now()

    print_header()

    # فحص المتطلبات
    if not check_requirements():
        return False

    # تشغيل تحليل الكتاب
    if not run_organized_analysis():
        return False

    # تشغيل إنشاء XML
    if not run_organized_xml_generation():
        return False

    # عرض الملفات المُنشأة
    list_generated_files()

    # طباعة تعليمات الاستيراد
    print_import_instructions()

    # طباعة الملخص النهائي
    print_final_summary()

    end_time = datetime.now()
    duration = end_time - start_time

    print(f"\n⏰ وقت التنفيذ: {duration.total_seconds():.1f} ثانية")
    print(f"🎉 تم إنجاز النظام المنظم بنجاح!")

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
