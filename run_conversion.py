#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main Conversion Script
Complete workflow for converting Arabic book to WordPress
"""

import os
import sys
from wordpress_converter import ArabicBookParser, WordPressXMLGenerator
from wordpress_helper import WordPressValidator, CategoryTreeGenerator, create_import_instructions

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 فحص المتطلبات...")
    
    # Check if document exists
    if not os.path.exists('Nahda.docx'):
        print("❌ لم يتم العثور على ملف Nahda.docx")
        return False
    
    # Check Python packages
    try:
        import docx
        print("✅ مكتبة python-docx متوفرة")
    except ImportError:
        print("❌ مكتبة python-docx غير متوفرة")
        print("   قم بتثبيتها باستخدام: pip install python-docx")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def run_full_conversion():
    """Run complete conversion process"""
    print("\n" + "="*60)
    print("🚀 بدء عملية التحويل الكاملة")
    print("   من: كتاب Word عربي")
    print("   إلى: تنسيق WordPress XML")
    print("="*60)
    
    # Step 1: Parse document
    print("\n📖 المرحلة 1: تحليل الوثيقة")
    print("-" * 30)
    
    parser = ArabicBookParser('Nahda.docx')
    
    if not parser.load_document():
        print("❌ فشل في تحميل الوثيقة")
        return False
    
    if not parser.extract_structure():
        print("❌ فشل في تحليل بنية الوثيقة")
        return False
    
    # Save structure
    if not parser.save_structure_json():
        print("❌ فشل في حفظ بنية الوثيقة")
        return False
    
    # Step 2: Validate structure
    print("\n🔍 المرحلة 2: التحقق من البنية")
    print("-" * 30)
    
    validator = WordPressValidator()
    if not validator.validate_hierarchy():
        print("⚠️ تم العثور على مشاكل في البنية، لكن سنتابع...")
    
    # Generate statistics
    stats = validator.generate_statistics()
    
    # Show category tree
    tree_gen = CategoryTreeGenerator(validator.structure)
    tree_gen.generate_tree()
    
    # Step 3: Generate WordPress XML
    print("\n🔧 المرحلة 3: إنشاء ملف WordPress XML")
    print("-" * 30)
    
    generator = WordPressXMLGenerator(parser.structure)
    
    if not generator.generate_xml():
        print("❌ فشل في إنشاء ملف XML")
        return False
    
    if not generator.save_xml():
        print("❌ فشل في حفظ ملف XML")
        return False
    
    # Step 4: Create additional files
    print("\n📋 المرحلة 4: إنشاء ملفات مساعدة")
    print("-" * 30)
    
    create_import_instructions()
    create_summary_report(stats)
    
    # Final success message
    print("\n" + "="*60)
    print("🎉 تم إكمال التحويل بنجاح!")
    print("="*60)
    
    print("\n📁 الملفات المُنشأة:")
    print("   ✅ book_structure.json - بنية الكتاب المُستخرجة")
    print("   ✅ wordpress_import.xml - ملف الاستيراد الرئيسي")
    print("   ✅ import_instructions.md - تعليمات الاستيراد")
    print("   ✅ conversion_summary.txt - ملخص عملية التحويل")
    
    print("\n📊 إحصائيات التحويل:")
    if stats:
        print(f"   📚 الأبواب: {stats.get('parts', 0)}")
        print(f"   📖 الفصول: {stats.get('chapters', 0)}")
        print(f"   📝 المباحث: {stats.get('sections', 0)}")
        print(f"   📄 وحدات المحتوى: {stats.get('content_units', 0)}")
        print(f"   📝 تقدير الكلمات: {stats.get('estimated_words', 0):,}")
    
    print("\n🚀 الخطوات التالية:")
    print("   1. راجع ملف import_instructions.md للتعليمات التفصيلية")
    print("   2. ارفع ملف wordpress_import.xml إلى WordPress")
    print("   3. تأكد من تفعيل دعم اللغة العربية")
    print("   4. اختبر عرض المحتوى والتنقل")
    
    return True

def create_summary_report(stats):
    """Create conversion summary report"""
    report = f"""
# تقرير تحويل كتاب النهضة إلى WordPress

## معلومات عامة
- تاريخ التحويل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- الملف المصدر: Nahda.docx
- حجم الوثيقة: 26,533 فقرة

## إحصائيات المحتوى
"""
    
    if stats:
        report += f"""
- إجمالي الأبواب: {stats.get('parts', 0)}
- إجمالي الفصول: {stats.get('chapters', 0)}
- إجمالي المباحث: {stats.get('sections', 0)}
- وحدات المحتوى: {stats.get('content_units', 0)}
- تقدير الكلمات: {stats.get('estimated_words', 0):,}
"""
    
    report += """
## الملفات المُنشأة
1. **book_structure.json** - بنية الكتاب بتنسيق JSON
2. **wordpress_import.xml** - ملف الاستيراد الرئيسي
3. **import_instructions.md** - تعليمات مفصلة للاستيراد
4. **conversion_summary.txt** - هذا التقرير

## بنية التصنيفات
- الأبواب: تصنيفات رئيسية
- الفصول: تصنيفات فرعية تحت الأبواب
- المحتوى: مقالات مُصنفة حسب الفصول

## ملاحظات تقنية
- تم الحفاظ على الترميز العربي (UTF-8)
- تم إنشاء بنية هرمية للتصنيفات
- تم تحويل المحتوى إلى HTML صالح
- تم إضافة metadata مناسب لكل مقال

## التوصيات
1. استخدم قالب WordPress يدعم RTL
2. تأكد من تفعيل اللغة العربية
3. استخدم خطوط عربية مناسبة
4. اختبر التنقل والبحث بعد الاستيراد
"""
    
    try:
        with open('conversion_summary.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print("✅ تم إنشاء تقرير التحويل: conversion_summary.txt")
    except Exception as e:
        print(f"⚠️ تعذر إنشاء تقرير التحويل: {e}")

def main():
    """Main execution function"""
    print("🌟 محول كتاب النهضة إلى WordPress")
    print("   نظام متكامل لتحويل الكتب العربية")
    
    # Check requirements
    if not check_requirements():
        print("\n❌ لا يمكن المتابعة بسبب متطلبات مفقودة")
        return False
    
    # Ask user confirmation
    print(f"\n📋 سيتم تحويل الكتاب التالي:")
    print(f"   📖 الملف: Nahda.docx")
    print(f"   📊 الحجم: ~26,533 فقرة")
    print(f"   🎯 الهدف: WordPress XML")
    
    response = input("\n❓ هل تريد المتابعة؟ (y/n): ").lower().strip()
    
    if response not in ['y', 'yes', 'نعم', 'ن']:
        print("❌ تم إلغاء العملية")
        return False
    
    # Run conversion
    success = run_full_conversion()
    
    if success:
        print("\n🎊 تمت العملية بنجاح! يمكنك الآن استيراد المحتوى إلى WordPress")
    else:
        print("\n💥 فشلت العملية. راجع الأخطاء أعلاه")
    
    return success

if __name__ == "__main__":
    from datetime import datetime
    main()
