#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح البنية الهرمية لـ WordPress - كتاب النهضة
استخدام طريقة مختلفة لضمان ربط Parent Category
"""

import json
import re
from datetime import datetime

class WordPressHierarchyFixer:
    """مصحح البنية الهرمية لـ WordPress"""
    
    def __init__(self):
        self.wp_categories = []
        self.wp_posts = []
        self.category_id_counter = 1
        self.post_id_counter = 1
        
    def load_organized_structure(self):
        """تحميل البنية المنظمة"""
        try:
            with open('book_structure_organized.json', 'r', encoding='utf-8') as f:
                self.book_structure = json.load(f)
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False
    
    def create_wordpress_categories(self):
        """إنشاء التصنيفات بطريقة WordPress الصحيحة"""
        print("🔧 إنشاء التصنيفات بطريقة WordPress الصحيحة...")
        
        # خريطة لربط IDs
        category_map = {}
        
        # 1. إنشاء تصنيف المقدمة
        if self.book_structure['introduction']:
            intro_cat = {
                'term_id': self.category_id_counter,
                'name': 'مقدمة الكتاب',
                'slug': 'introduction',
                'parent': 0,
                'parent_name': ''
            }
            self.wp_categories.append(intro_cat)
            category_map['introduction'] = intro_cat
            self.category_id_counter += 1
            print(f"📚 مقدمة: {intro_cat['name']} (ID: {intro_cat['term_id']})")
        
        # 2. إنشاء الأقسام الرئيسية
        part_map = {}
        for part in self.book_structure['parts']:
            part_slug = self.create_slug(part['title'])
            part_cat = {
                'term_id': self.category_id_counter,
                'name': self.clean_title(part['title']),
                'slug': part_slug,
                'parent': 0,
                'parent_name': ''
            }
            self.wp_categories.append(part_cat)
            part_map[part['id']] = part_cat
            category_map[part_slug] = part_cat
            self.category_id_counter += 1
            print(f"📚 قسم: {part_cat['name']} (ID: {part_cat['term_id']})")
        
        # 3. إنشاء الأبواب تحت الأقسام
        chapter_map = {}
        for chapter in self.book_structure['chapters']:
            chapter_slug = self.create_slug(chapter['title'])
            parent_id = 0
            parent_name = ''
            
            if chapter.get('parent_part_id') and chapter['parent_part_id'] in part_map:
                parent_cat = part_map[chapter['parent_part_id']]
                parent_id = parent_cat['term_id']
                parent_name = parent_cat['name']
            
            chapter_cat = {
                'term_id': self.category_id_counter,
                'name': self.clean_title(chapter['title']),
                'slug': chapter_slug,
                'parent': parent_id,
                'parent_name': parent_name
            }
            self.wp_categories.append(chapter_cat)
            chapter_map[chapter['id']] = chapter_cat
            category_map[chapter_slug] = chapter_cat
            self.category_id_counter += 1
            print(f"  📖 باب: {chapter_cat['name']} (ID: {chapter_cat['term_id']}, الأب: {parent_name})")
        
        # 4. إنشاء المقالات
        for section in self.book_structure['sections']:
            category_id = category_map.get('introduction', {}).get('term_id', 1)
            category_slug = 'introduction'
            
            if section.get('parent_chapter_id') and section['parent_chapter_id'] in chapter_map:
                parent_cat = chapter_map[section['parent_chapter_id']]
                category_id = parent_cat['term_id']
                category_slug = parent_cat['slug']
            elif section.get('parent_part_id') and section['parent_part_id'] in part_map:
                parent_cat = part_map[section['parent_part_id']]
                category_id = parent_cat['term_id']
                category_slug = parent_cat['slug']
            
            section_post = {
                'id': self.post_id_counter,
                'title': self.clean_title(section['title']),
                'content': section['html_content'],
                'category_id': category_id,
                'category_slug': category_slug,
                'slug': self.create_slug(section['title']),
                'excerpt': self.create_excerpt(section['html_content']),
                'word_count': section['word_count']
            }
            self.wp_posts.append(section_post)
            self.post_id_counter += 1
        
        print(f"✅ تم إنشاء البنية:")
        print(f"   📂 التصنيفات: {len(self.wp_categories)}")
        print(f"   📄 المقالات: {len(self.wp_posts)}")
    
    def clean_title(self, title):
        """تنظيف العنوان"""
        cleaned = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', '', title)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned[:100]
    
    def create_slug(self, text):
        """إنشاء slug للروابط"""
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-').lower()[:50]
    
    def create_excerpt(self, content, max_length=300):
        """إنشاء مقتطف من المحتوى"""
        text_content = re.sub(r'<[^>]+>', '', content)
        
        if len(text_content) <= max_length:
            return text_content
        
        excerpt = text_content[:max_length]
        last_sentence = excerpt.rfind('.')
        last_space = excerpt.rfind(' ')
        
        if last_sentence > max_length - 100:
            return text_content[:last_sentence + 1]
        elif last_space > max_length - 50:
            return text_content[:last_space] + "..."
        else:
            return text_content[:max_length - 3] + "..."
    
    def create_xml_header(self):
        """إنشاء رأس ملف XML"""
        return '''<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0"
    xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:wfw="http://wellformedweb.org/CommentAPI/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:wp="http://wordpress.org/export/1.2/">

<channel>
    <title>كتاب النهضة - البنية الهرمية المصححة لـ WordPress</title>
    <link>http://localhost</link>
    <description>مشروع النهضة وبناء الدولة السورية - Parent Category مصحح</description>
    <pubDate>{}</pubDate>
    <language>ar</language>
    <wp:wxr_version>1.2</wp:wxr_version>
    <wp:base_site_url>http://localhost</wp:base_site_url>
    <wp:base_blog_url>http://localhost</wp:base_blog_url>

'''.format(datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000'))
    
    def create_category_xml_wordpress_style(self, category):
        """إنشاء XML للتصنيف بطريقة WordPress الصحيحة"""
        # استخدام parent_name بدلاً من parent_id إذا كان متوفراً
        parent_value = category['parent_name'] if category['parent_name'] else category['parent']
        
        return f'''    <wp:category>
        <wp:term_id>{category['term_id']}</wp:term_id>
        <wp:category_nicename>{category['slug']}</wp:category_nicename>
        <wp:category_parent>{parent_value}</wp:category_parent>
        <wp:cat_name><![CDATA[{category['name']}]]></wp:cat_name>
    </wp:category>

'''
    
    def create_post_xml(self, post):
        """إنشاء XML للمقال"""
        category_name = self.get_category_name_by_id(post['category_id'])
        
        return f'''    <item>
        <title><![CDATA[{post['title']}]]></title>
        <link>http://localhost/{post['slug']}/</link>
        <pubDate>{datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}</pubDate>
        <dc:creator><![CDATA[admin]]></dc:creator>
        <guid isPermaLink="false">http://localhost/?p={post['id']}</guid>
        <description></description>
        <content:encoded><![CDATA[{post['content']}]]></content:encoded>
        <excerpt:encoded><![CDATA[{post['excerpt']}]]></excerpt:encoded>
        <wp:post_id>{post['id']}</wp:post_id>
        <wp:post_date>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date>
        <wp:post_date_gmt>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date_gmt>
        <wp:comment_status>open</wp:comment_status>
        <wp:ping_status>open</wp:ping_status>
        <wp:post_name>{post['slug']}</wp:post_name>
        <wp:status>publish</wp:status>
        <wp:post_parent>0</wp:post_parent>
        <wp:menu_order>0</wp:menu_order>
        <wp:post_type>post</wp:post_type>
        <wp:post_password></wp:post_password>
        <wp:is_sticky>0</wp:is_sticky>
        <category domain="category" nicename="{post['category_slug']}"><![CDATA[{category_name}]]></category>
    </item>

'''
    
    def get_category_name_by_id(self, category_id):
        """الحصول على اسم التصنيف بالـ ID"""
        for cat in self.wp_categories:
            if cat['term_id'] == category_id:
                return cat['name']
        return 'غير محدد'
    
    def create_xml_footer(self):
        """إنشاء ذيل ملف XML"""
        return '''</channel>
</rss>'''
    
    def generate_wordpress_categories_file(self):
        """إنشاء ملف التصنيفات بطريقة WordPress الصحيحة"""
        print("📂 إنشاء ملف التصنيفات بطريقة WordPress الصحيحة...")
        
        xml_content = self.create_xml_header()
        
        # ترتيب التصنيفات: الآباء أولاً
        sorted_categories = sorted(self.wp_categories, key=lambda x: (x['parent'], x['term_id']))
        
        for category in sorted_categories:
            xml_content += self.create_category_xml_wordpress_style(category)
        
        xml_content += self.create_xml_footer()
        
        filename = 'wordpress_fixed_categories.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        file_size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB - {len(self.wp_categories)} تصنيف)")
        
        return filename
    
    def generate_posts_files(self, max_file_size_mb=2):
        """إنشاء ملفات المقالات"""
        print("📄 إنشاء ملفات المقالات...")
        
        max_file_size = max_file_size_mb * 1024 * 1024
        files_created = []
        
        current_file_posts = []
        current_file_size = 0
        file_number = 1
        
        header_size = len(self.create_xml_header().encode('utf-8'))
        footer_size = len(self.create_xml_footer().encode('utf-8'))
        
        for post in self.wp_posts:
            post_xml = self.create_post_xml(post)
            post_size = len(post_xml.encode('utf-8'))
            
            if current_file_size + post_size + header_size + footer_size > max_file_size and current_file_posts:
                filename = self.save_posts_file(current_file_posts, file_number)
                files_created.append(filename)
                
                current_file_posts = [post]
                current_file_size = post_size
                file_number += 1
            else:
                current_file_posts.append(post)
                current_file_size += post_size
        
        if current_file_posts:
            filename = self.save_posts_file(current_file_posts, file_number)
            files_created.append(filename)
        
        return files_created
    
    def save_posts_file(self, posts, file_number):
        """حفظ ملف المقالات"""
        xml_content = self.create_xml_header()
        
        for post in posts:
            xml_content += self.create_post_xml(post)
        
        xml_content += self.create_xml_footer()
        
        filename = f'wordpress_posts_part_{file_number:02d}.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        file_size = os.path.getsize(filename) / 1024
        avg_words = sum(post['word_count'] for post in posts) // len(posts) if posts else 0
        
        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB - {len(posts)} مقال)")
        print(f"   📊 متوسط الكلمات: {avg_words} كلمة/مقال")
        
        return filename
    
    def print_hierarchy_test(self):
        """طباعة اختبار البنية الهرمية"""
        print(f"\n🧪 اختبار البنية الهرمية:")
        
        # التصنيفات الرئيسية
        root_cats = [cat for cat in self.wp_categories if cat['parent'] == 0]
        print(f"   📚 التصنيفات الرئيسية: {len(root_cats)}")
        
        # التصنيفات الفرعية
        child_cats = [cat for cat in self.wp_categories if cat['parent'] != 0]
        print(f"   📖 التصنيفات الفرعية: {len(child_cats)}")
        
        # عرض عينة
        print(f"\n🌳 عينة من البنية:")
        for root_cat in root_cats[:3]:
            print(f"📚 {root_cat['name']} (ID: {root_cat['term_id']})")
            
            children = [cat for cat in self.wp_categories if cat['parent'] == root_cat['term_id']]
            for child in children[:2]:
                print(f"  📖 {child['name']} (ID: {child['term_id']}, الأب: {child['parent_name']})")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح البنية الهرمية لـ WordPress - كتاب النهضة")
    print("=" * 70)
    print("🎯 الهدف: ضمان ظهور Parent Category بشكل صحيح في WordPress")
    print("📋 الطريقة: استخدام parent_name بدلاً من parent_id")
    print("=" * 70)
    
    fixer = WordPressHierarchyFixer()
    
    if not fixer.load_organized_structure():
        return False
    
    fixer.create_wordpress_categories()
    fixer.print_hierarchy_test()
    
    print(f"\n🔧 إنشاء ملفات XML لـ WordPress...")
    
    categories_file = fixer.generate_wordpress_categories_file()
    posts_files = fixer.generate_posts_files()
    
    print(f"\n🎉 تم إصلاح البنية الهرمية لـ WordPress بنجاح!")
    print(f"📁 الملفات المُنشأة: {1 + len(posts_files)} ملف")
    print(f"📂 التصنيفات: {len(fixer.wp_categories)}")
    print(f"📄 المقالات: {len(fixer.wp_posts)}")
    
    print(f"\n💡 تعليمات الاستيراد:")
    print(f"1. احذف جميع التصنيفات والمقالات الموجودة")
    print(f"2. استورد {categories_file} أولاً")
    print(f"3. تحقق من Parent Category")
    print(f"4. استورد ملفات المقالات:")
    for posts_file in posts_files:
        print(f"   - {posts_file}")
    
    return True

if __name__ == "__main__":
    import os
    main()
