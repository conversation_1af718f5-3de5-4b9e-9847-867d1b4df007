<?php
/**
 * خارطة تصفح كتاب النهضة - WordPress Plugin
 * إنشاء خارطة تفاعلية للكتاب مع البنية الهرمية
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

class NahdaBookMap {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_shortcode('nahda_book_map', array($this, 'display_book_map'));
        add_shortcode('nahda_category_tree', array($this, 'display_category_tree'));
        add_shortcode('nahda_breadcrumb', array($this, 'display_breadcrumb'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function init() {
        // إضافة صفحة خارطة الكتاب
        $this->create_book_map_page();
    }
    
    public function enqueue_scripts() {
        wp_enqueue_script('jquery');
        wp_enqueue_style('nahda-book-map', plugin_dir_url(__FILE__) . 'nahda-book-map.css');
        wp_enqueue_script('nahda-book-map', plugin_dir_url(__FILE__) . 'nahda-book-map.js', array('jquery'));
    }
    
    /**
     * إنشاء صفحة خارطة الكتاب تلقائياً
     */
    public function create_book_map_page() {
        $page_title = 'خارطة كتاب النهضة';
        $page_slug = 'nahda-book-map';
        
        // التحقق من وجود الصفحة
        $existing_page = get_page_by_path($page_slug);
        
        if (!$existing_page) {
            $page_content = '[nahda_book_map]';
            
            $page_data = array(
                'post_title'    => $page_title,
                'post_content'  => $page_content,
                'post_status'   => 'publish',
                'post_type'     => 'page',
                'post_name'     => $page_slug,
                'post_author'   => 1,
            );
            
            wp_insert_post($page_data);
        }
    }
    
    /**
     * عرض خارطة الكتاب الكاملة
     */
    public function display_book_map($atts) {
        $atts = shortcode_atts(array(
            'style' => 'tree', // tree, grid, accordion
            'show_counts' => 'true',
            'max_depth' => 5
        ), $atts);
        
        ob_start();
        
        echo '<div class="nahda-book-map">';
        echo '<h2>🗺️ خارطة كتاب مشروع النهضة السورية</h2>';
        
        // إحصائيات سريعة
        $this->display_book_statistics();
        
        // البحث السريع
        $this->display_quick_search();
        
        // الخارطة الرئيسية
        if ($atts['style'] === 'tree') {
            $this->display_tree_map($atts);
        } elseif ($atts['style'] === 'grid') {
            $this->display_grid_map($atts);
        } elseif ($atts['style'] === 'accordion') {
            $this->display_accordion_map($atts);
        }
        
        echo '</div>';
        
        return ob_get_clean();
    }
    
    /**
     * عرض إحصائيات الكتاب
     */
    private function display_book_statistics() {
        $categories = get_categories(array('hide_empty' => false));
        $posts_count = wp_count_posts()->publish;
        
        // تصنيف حسب المستوى
        $levels = array();
        foreach ($categories as $cat) {
            $level = $this->get_category_level($cat);
            if (!isset($levels[$level])) {
                $levels[$level] = 0;
            }
            $levels[$level]++;
        }
        
        echo '<div class="book-statistics">';
        echo '<h3>📊 إحصائيات الكتاب</h3>';
        echo '<div class="stats-grid">';
        
        $level_names = array(
            1 => 'الأقسام',
            2 => 'الأبواب', 
            3 => 'الفصول',
            4 => 'المباحث',
            5 => 'المقالات'
        );
        
        foreach ($levels as $level => $count) {
            $name = isset($level_names[$level]) ? $level_names[$level] : "المستوى $level";
            echo "<div class='stat-item'>";
            echo "<span class='stat-number'>$count</span>";
            echo "<span class='stat-label'>$name</span>";
            echo "</div>";
        }
        
        echo "<div class='stat-item'>";
        echo "<span class='stat-number'>$posts_count</span>";
        echo "<span class='stat-label'>إجمالي المقالات</span>";
        echo "</div>";
        
        echo '</div></div>';
    }
    
    /**
     * عرض البحث السريع
     */
    private function display_quick_search() {
        echo '<div class="quick-search">';
        echo '<h3>🔍 البحث السريع</h3>';
        echo '<input type="text" id="book-search" placeholder="ابحث في الكتاب..." />';
        echo '<div id="search-results"></div>';
        echo '</div>';
    }
    
    /**
     * عرض الخارطة الشجرية
     */
    private function display_tree_map($atts) {
        echo '<div class="tree-map">';
        echo '<h3>🌳 البنية الهرمية للكتاب</h3>';
        
        // الحصول على الأقسام الرئيسية (المستوى الأول)
        $main_categories = get_categories(array(
            'parent' => 0,
            'hide_empty' => false,
            'orderby' => 'term_id'
        ));
        
        echo '<ul class="book-tree level-1">';
        foreach ($main_categories as $category) {
            $this->display_category_tree_item($category, 1, $atts);
        }
        echo '</ul>';
        
        echo '</div>';
    }
    
    /**
     * عرض عنصر في الشجرة
     */
    private function display_category_tree_item($category, $level, $atts) {
        $max_depth = intval($atts['max_depth']);
        if ($level > $max_depth) return;
        
        $show_counts = $atts['show_counts'] === 'true';
        $posts_count = $this->get_category_posts_count($category->term_id);
        $children = get_categories(array(
            'parent' => $category->term_id,
            'hide_empty' => false,
            'orderby' => 'term_id'
        ));
        
        $level_icon = $this->get_level_icon($level);
        $category_url = get_category_link($category->term_id);
        
        echo '<li class="tree-item level-' . $level . '">';
        echo '<div class="item-header">';
        
        if (!empty($children)) {
            echo '<span class="toggle-btn" data-target="children-' . $category->term_id . '">▼</span>';
        } else {
            echo '<span class="no-toggle">•</span>';
        }
        
        echo '<span class="level-icon">' . $level_icon . '</span>';
        echo '<a href="' . $category_url . '" class="category-link">' . $category->name . '</a>';
        
        if ($show_counts && $posts_count > 0) {
            echo '<span class="posts-count">(' . $posts_count . ' مقال)</span>';
        }
        
        echo '</div>';
        
        if (!empty($children)) {
            echo '<ul class="children" id="children-' . $category->term_id . '">';
            foreach ($children as $child) {
                $this->display_category_tree_item($child, $level + 1, $atts);
            }
            echo '</ul>';
        }
        
        echo '</li>';
    }
    
    /**
     * عرض الخارطة الشبكية
     */
    private function display_grid_map($atts) {
        echo '<div class="grid-map">';
        echo '<h3>📋 عرض شبكي للكتاب</h3>';
        
        $main_categories = get_categories(array(
            'parent' => 0,
            'hide_empty' => false,
            'orderby' => 'term_id'
        ));
        
        echo '<div class="categories-grid">';
        foreach ($main_categories as $category) {
            $this->display_category_card($category);
        }
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * عرض بطاقة تصنيف
     */
    private function display_category_card($category) {
        $posts_count = $this->get_category_posts_count($category->term_id);
        $children = get_categories(array(
            'parent' => $category->term_id,
            'hide_empty' => false
        ));
        
        $category_url = get_category_link($category->term_id);
        
        echo '<div class="category-card">';
        echo '<h4><a href="' . $category_url . '">' . $category->name . '</a></h4>';
        echo '<div class="card-stats">';
        echo '<span>📄 ' . $posts_count . ' مقال</span>';
        echo '<span>📂 ' . count($children) . ' قسم فرعي</span>';
        echo '</div>';
        
        if (!empty($children)) {
            echo '<ul class="subcategories">';
            foreach (array_slice($children, 0, 5) as $child) {
                echo '<li><a href="' . get_category_link($child->term_id) . '">' . $child->name . '</a></li>';
            }
            if (count($children) > 5) {
                echo '<li>... و ' . (count($children) - 5) . ' أقسام أخرى</li>';
            }
            echo '</ul>';
        }
        
        echo '</div>';
    }
    
    /**
     * عرض الخارطة الأكورديون
     */
    private function display_accordion_map($atts) {
        echo '<div class="accordion-map">';
        echo '<h3>📚 عرض أكورديون للكتاب</h3>';
        
        $main_categories = get_categories(array(
            'parent' => 0,
            'hide_empty' => false,
            'orderby' => 'term_id'
        ));
        
        echo '<div class="accordion-container">';
        foreach ($main_categories as $index => $category) {
            $this->display_accordion_section($category, $index);
        }
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * عرض قسم أكورديون
     */
    private function display_accordion_section($category, $index) {
        $posts_count = $this->get_category_posts_count($category->term_id);
        $children = get_categories(array(
            'parent' => $category->term_id,
            'hide_empty' => false,
            'orderby' => 'term_id'
        ));
        
        echo '<div class="accordion-section">';
        echo '<div class="accordion-header" data-target="accordion-content-' . $index . '">';
        echo '<h4>' . $category->name . '</h4>';
        echo '<span class="accordion-toggle">▼</span>';
        echo '</div>';
        
        echo '<div class="accordion-content" id="accordion-content-' . $index . '">';
        
        if ($posts_count > 0) {
            echo '<p><strong>📄 ' . $posts_count . ' مقال في هذا القسم</strong></p>';
        }
        
        if (!empty($children)) {
            echo '<ul class="subcategories-list">';
            foreach ($children as $child) {
                $child_posts = $this->get_category_posts_count($child->term_id);
                echo '<li>';
                echo '<a href="' . get_category_link($child->term_id) . '">' . $child->name . '</a>';
                if ($child_posts > 0) {
                    echo ' <span class="count">(' . $child_posts . ')</span>';
                }
                echo '</li>';
            }
            echo '</ul>';
        }
        
        echo '<a href="' . get_category_link($category->term_id) . '" class="view-category-btn">عرض جميع مقالات هذا القسم</a>';
        
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * عرض مسار التنقل (Breadcrumb)
     */
    public function display_breadcrumb($atts) {
        if (!is_category() && !is_single()) {
            return '';
        }
        
        ob_start();
        
        echo '<div class="nahda-breadcrumb">';
        echo '<span class="breadcrumb-home"><a href="' . home_url() . '">🏠 الرئيسية</a></span>';
        echo '<span class="separator"> › </span>';
        echo '<span class="breadcrumb-map"><a href="' . home_url('/nahda-book-map/') . '">📖 خارطة الكتاب</a></span>';
        
        if (is_category()) {
            $category = get_queried_object();
            $this->display_category_breadcrumb($category);
        } elseif (is_single()) {
            $post = get_queried_object();
            $categories = get_the_category($post->ID);
            if (!empty($categories)) {
                $this->display_category_breadcrumb($categories[0]);
                echo '<span class="separator"> › </span>';
                echo '<span class="breadcrumb-current">' . get_the_title() . '</span>';
            }
        }
        
        echo '</div>';
        
        return ob_get_clean();
    }
    
    /**
     * عرض مسار التصنيف
     */
    private function display_category_breadcrumb($category) {
        $parents = array();
        $current = $category;
        
        while ($current && $current->parent != 0) {
            $parents[] = $current;
            $current = get_category($current->parent);
        }
        
        if ($current) {
            $parents[] = $current;
        }
        
        $parents = array_reverse($parents);
        
        foreach ($parents as $parent) {
            echo '<span class="separator"> › </span>';
            if ($parent->term_id === $category->term_id) {
                echo '<span class="breadcrumb-current">' . $parent->name . '</span>';
            } else {
                echo '<span class="breadcrumb-item"><a href="' . get_category_link($parent->term_id) . '">' . $parent->name . '</a></span>';
            }
        }
    }
    
    /**
     * الحصول على مستوى التصنيف
     */
    private function get_category_level($category) {
        $level = 1;
        $current = $category;
        
        while ($current->parent != 0) {
            $level++;
            $current = get_category($current->parent);
        }
        
        return $level;
    }
    
    /**
     * الحصول على أيقونة المستوى
     */
    private function get_level_icon($level) {
        $icons = array(
            1 => '📚', // قسم
            2 => '📖', // باب
            3 => '📝', // فصل
            4 => '🔸', // مبحث
            5 => '📄'  // مقال
        );
        
        return isset($icons[$level]) ? $icons[$level] : '•';
    }
    
    /**
     * الحصول على عدد المقالات في التصنيف
     */
    private function get_category_posts_count($category_id) {
        $posts = get_posts(array(
            'category' => $category_id,
            'numberposts' => -1,
            'post_status' => 'publish'
        ));
        
        return count($posts);
    }
}

// تفعيل الكلاس
new NahdaBookMap();

/**
 * دالة مساعدة لعرض خارطة الكتاب في أي مكان
 */
function nahda_display_book_map($style = 'tree') {
    echo do_shortcode('[nahda_book_map style="' . $style . '"]');
}

/**
 * دالة مساعدة لعرض مسار التنقل
 */
function nahda_display_breadcrumb() {
    echo do_shortcode('[nahda_breadcrumb]');
}
?>
