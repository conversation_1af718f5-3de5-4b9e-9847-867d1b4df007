<?php
/**
 * Plugin Name: خارطة كتاب النهضة
 * Plugin URI: https://nahda-project.org
 * Description: خارطة تفاعلية لكتاب مشروع النهضة السورية مع البنية الهرمية الكاملة (قسم → باب → فصل → مبحث → مقال)
 * Version: 1.0.0
 * Author: مطور مشروع النهضة
 * Author URI: https://nahda-project.org
 * Text Domain: nahda-book-map
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تعريف الثوابت
define('NAHDA_BOOK_MAP_VERSION', '1.0.0');
define('NAHDA_BOOK_MAP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('NAHDA_BOOK_MAP_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * الكلاس الرئيسي لخارطة كتاب النهضة
 */
class NahdaBookMapPlugin {
    
    /**
     * المثيل الوحيد للكلاس
     */
    private static $instance = null;
    
    /**
     * الحصول على المثيل الوحيد
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * البناء
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // تسجيل Shortcodes
        add_shortcode('nahda_book_map', array($this, 'display_book_map'));
        add_shortcode('nahda_category_tree', array($this, 'display_category_tree'));
        add_shortcode('nahda_breadcrumb', array($this, 'display_breadcrumb'));
        add_shortcode('nahda_book_stats', array($this, 'display_book_stats'));
        
        // تفعيل عند التثبيت
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * التهيئة
     */
    public function init() {
        // تحميل الترجمات
        load_plugin_textdomain('nahda-book-map', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // إنشاء صفحة خارطة الكتاب
        $this->create_book_map_page();
        
        // إضافة دعم للقوائم
        $this->add_menu_support();
    }
    
    /**
     * تحميل الملفات والأنماط
     */
    public function enqueue_scripts() {
        // تحميل jQuery
        wp_enqueue_script('jquery');
        
        // تحميل ملفات Plugin
        wp_enqueue_style(
            'nahda-book-map-css',
            NAHDA_BOOK_MAP_PLUGIN_URL . 'assets/nahda-book-map.css',
            array(),
            NAHDA_BOOK_MAP_VERSION
        );
        
        wp_enqueue_script(
            'nahda-book-map-js',
            NAHDA_BOOK_MAP_PLUGIN_URL . 'assets/nahda-book-map.js',
            array('jquery'),
            NAHDA_BOOK_MAP_VERSION,
            true
        );
        
        // تمرير البيانات لـ JavaScript
        wp_localize_script('nahda-book-map-js', 'nahdaBookMap', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('nahda_book_map_nonce'),
            'strings' => array(
                'searching' => __('جاري البحث...', 'nahda-book-map'),
                'noResults' => __('لم يتم العثور على نتائج', 'nahda-book-map'),
                'expandAll' => __('توسيع الكل', 'nahda-book-map'),
                'collapseAll' => __('طي الكل', 'nahda-book-map')
            )
        ));
    }
    
    /**
     * إضافة قائمة الإدارة
     */
    public function add_admin_menu() {
        add_options_page(
            __('خارطة كتاب النهضة', 'nahda-book-map'),
            __('خارطة الكتاب', 'nahda-book-map'),
            'manage_options',
            'nahda-book-map',
            array($this, 'admin_page')
        );
    }
    
    /**
     * صفحة الإدارة
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('خارطة كتاب النهضة', 'nahda-book-map'); ?></h1>
            
            <div class="card">
                <h2><?php _e('الاستخدام', 'nahda-book-map'); ?></h2>
                <p><?php _e('استخدم الـ Shortcodes التالية لعرض خارطة الكتاب:', 'nahda-book-map'); ?></p>
                
                <h3><?php _e('الخارطة الكاملة', 'nahda-book-map'); ?></h3>
                <code>[nahda_book_map style="tree"]</code>
                <p><?php _e('عرض شجري تفاعلي للكتاب', 'nahda-book-map'); ?></p>
                
                <code>[nahda_book_map style="grid"]</code>
                <p><?php _e('عرض شبكي بالبطاقات', 'nahda-book-map'); ?></p>
                
                <code>[nahda_book_map style="accordion"]</code>
                <p><?php _e('عرض أكورديون مضغوط', 'nahda-book-map'); ?></p>
                
                <h3><?php _e('عناصر إضافية', 'nahda-book-map'); ?></h3>
                <code>[nahda_category_tree]</code>
                <p><?php _e('شجرة التصنيفات فقط', 'nahda-book-map'); ?></p>
                
                <code>[nahda_breadcrumb]</code>
                <p><?php _e('مسار التنقل', 'nahda-book-map'); ?></p>
                
                <code>[nahda_book_stats]</code>
                <p><?php _e('إحصائيات الكتاب', 'nahda-book-map'); ?></p>
            </div>
            
            <div class="card">
                <h2><?php _e('الإحصائيات', 'nahda-book-map'); ?></h2>
                <?php $this->display_admin_stats(); ?>
            </div>
            
            <div class="card">
                <h2><?php _e('الصفحات المُنشأة', 'nahda-book-map'); ?></h2>
                <?php $this->display_created_pages(); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * عرض الإحصائيات في الإدارة
     */
    private function display_admin_stats() {
        $categories = get_categories(array('hide_empty' => false));
        $posts_count = wp_count_posts()->publish;
        
        // تصنيف حسب المستوى
        $levels = array();
        foreach ($categories as $cat) {
            $level = $this->get_category_level($cat);
            if (!isset($levels[$level])) {
                $levels[$level] = 0;
            }
            $levels[$level]++;
        }
        
        echo '<table class="widefat">';
        echo '<thead><tr><th>المستوى</th><th>النوع</th><th>العدد</th></tr></thead>';
        echo '<tbody>';
        
        $level_names = array(
            1 => 'الأقسام',
            2 => 'الأبواب',
            3 => 'الفصول',
            4 => 'المباحث'
        );
        
        foreach ($levels as $level => $count) {
            $name = isset($level_names[$level]) ? $level_names[$level] : "المستوى $level";
            echo "<tr><td>$level</td><td>$name</td><td>$count</td></tr>";
        }
        
        echo "<tr><td>5</td><td>المقالات</td><td>$posts_count</td></tr>";
        echo '</tbody></table>';
    }
    
    /**
     * عرض الصفحات المُنشأة
     */
    private function display_created_pages() {
        $pages = array(
            'nahda-book-map' => 'خارطة كتاب النهضة',
            'book-index' => 'فهرس الكتاب',
            'book-contents' => 'محتويات الكتاب'
        );
        
        echo '<ul>';
        foreach ($pages as $slug => $title) {
            $page = get_page_by_path($slug);
            if ($page) {
                $url = get_permalink($page->ID);
                echo "<li>✅ <a href='$url' target='_blank'>$title</a></li>";
            } else {
                echo "<li>❌ $title (غير موجودة)</li>";
            }
        }
        echo '</ul>';
    }
    
    /**
     * إنشاء صفحة خارطة الكتاب
     */
    public function create_book_map_page() {
        $pages = array(
            array(
                'title' => 'خارطة كتاب النهضة',
                'slug' => 'nahda-book-map',
                'content' => '[nahda_book_map style="tree"]'
            ),
            array(
                'title' => 'فهرس الكتاب',
                'slug' => 'book-index',
                'content' => '[nahda_book_map style="grid"]'
            ),
            array(
                'title' => 'محتويات الكتاب',
                'slug' => 'book-contents',
                'content' => '[nahda_book_map style="accordion"]'
            )
        );
        
        foreach ($pages as $page_data) {
            $existing_page = get_page_by_path($page_data['slug']);
            
            if (!$existing_page) {
                $page_args = array(
                    'post_title' => $page_data['title'],
                    'post_content' => $page_data['content'],
                    'post_status' => 'publish',
                    'post_type' => 'page',
                    'post_name' => $page_data['slug'],
                    'post_author' => 1,
                );
                
                wp_insert_post($page_args);
            }
        }
    }
    
    /**
     * إضافة دعم للقوائم
     */
    public function add_menu_support() {
        // إضافة خارطة الكتاب للقوائم
        add_action('wp_nav_menu_items', array($this, 'add_book_map_to_menu'), 10, 2);
    }
    
    /**
     * إضافة رابط خارطة الكتاب للقائمة
     */
    public function add_book_map_to_menu($items, $args) {
        // يمكن تخصيص هذا حسب الحاجة
        return $items;
    }
    
    /**
     * عرض خارطة الكتاب - Shortcode
     */
    public function display_book_map($atts) {
        $atts = shortcode_atts(array(
            'style' => 'tree',
            'show_counts' => 'true',
            'max_depth' => 5,
            'show_search' => 'true',
            'show_stats' => 'true'
        ), $atts);
        
        ob_start();
        
        echo '<div class="nahda-book-map">';
        echo '<h2>🗺️ خارطة كتاب مشروع النهضة السورية</h2>';
        
        // الإحصائيات
        if ($atts['show_stats'] === 'true') {
            $this->display_book_statistics();
        }
        
        // البحث
        if ($atts['show_search'] === 'true') {
            $this->display_quick_search();
        }
        
        // الخارطة حسب النوع
        switch ($atts['style']) {
            case 'grid':
                $this->display_grid_map($atts);
                break;
            case 'accordion':
                $this->display_accordion_map($atts);
                break;
            default:
                $this->display_tree_map($atts);
                break;
        }
        
        echo '</div>';
        
        return ob_get_clean();
    }
    
    /**
     * عرض شجرة التصنيفات - Shortcode
     */
    public function display_category_tree($atts) {
        $atts = shortcode_atts(array(
            'max_depth' => 3,
            'show_counts' => 'true'
        ), $atts);
        
        ob_start();
        
        echo '<div class="nahda-category-tree">';
        $this->display_tree_map($atts);
        echo '</div>';
        
        return ob_get_clean();
    }
    
    /**
     * عرض مسار التنقل - Shortcode
     */
    public function display_breadcrumb($atts) {
        if (!is_category() && !is_single()) {
            return '';
        }
        
        ob_start();
        
        echo '<div class="nahda-breadcrumb">';
        echo '<span class="breadcrumb-home"><a href="' . home_url() . '">🏠 الرئيسية</a></span>';
        echo '<span class="separator"> › </span>';
        echo '<span class="breadcrumb-map"><a href="' . home_url('/nahda-book-map/') . '">📖 خارطة الكتاب</a></span>';
        
        if (is_category()) {
            $category = get_queried_object();
            $this->display_category_breadcrumb($category);
        } elseif (is_single()) {
            $post = get_queried_object();
            $categories = get_the_category($post->ID);
            if (!empty($categories)) {
                $this->display_category_breadcrumb($categories[0]);
                echo '<span class="separator"> › </span>';
                echo '<span class="breadcrumb-current">' . get_the_title() . '</span>';
            }
        }
        
        echo '</div>';
        
        return ob_get_clean();
    }
    
    /**
     * عرض إحصائيات الكتاب - Shortcode
     */
    public function display_book_stats($atts) {
        ob_start();
        
        echo '<div class="nahda-book-stats">';
        $this->display_book_statistics();
        echo '</div>';
        
        return ob_get_clean();
    }
    
    // تضمين باقي الدوال من الملف الأصلي...
    // (سيتم إضافتها في الجزء التالي)
    
    /**
     * تفعيل Plugin
     */
    public function activate() {
        // إنشاء الصفحات
        $this->create_book_map_page();
        
        // تحديث قواعد الروابط
        flush_rewrite_rules();
    }
    
    /**
     * إلغاء تفعيل Plugin
     */
    public function deactivate() {
        // تنظيف مؤقت
        flush_rewrite_rules();
    }
    
    /**
     * دوال مساعدة
     */
    private function get_category_level($category) {
        $level = 1;
        $current = $category;
        
        while ($current->parent != 0) {
            $level++;
            $current = get_category($current->parent);
        }
        
        return $level;
    }
    
    // باقي الدوال المساعدة...
    // (سيتم نسخها من الملف الأصلي)
}

// تشغيل Plugin
function nahda_book_map_init() {
    return NahdaBookMapPlugin::get_instance();
}

// تفعيل Plugin
add_action('plugins_loaded', 'nahda_book_map_init');

// دوال مساعدة للاستخدام في القوالب
function nahda_display_book_map($style = 'tree') {
    echo do_shortcode('[nahda_book_map style="' . $style . '"]');
}

function nahda_display_breadcrumb() {
    echo do_shortcode('[nahda_breadcrumb]');
}

function nahda_display_book_stats() {
    echo do_shortcode('[nahda_book_stats]');
}
?>
