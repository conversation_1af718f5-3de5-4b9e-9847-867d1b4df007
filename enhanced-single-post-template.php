<?php
/**
 * قالب محسن لعرض المقالات الكاملة
 * Template Name: مقال النهضة المحسن
 */

get_header(); ?>

<div class="nahda-enhanced-post-container">
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
        
        <!-- مسار التنقل -->
        <div class="nahda-breadcrumb-wrapper">
            <?php echo do_shortcode('[nahda_breadcrumb]'); ?>
        </div>
        
        <!-- المقال الرئيسي -->
        <article id="post-<?php the_ID(); ?>" <?php post_class('nahda-enhanced-article'); ?>>
            
            <!-- رأس المقال -->
            <header class="enhanced-article-header">
                <div class="article-meta-top">
                    <?php
                    $categories = get_the_category();
                    if (!empty($categories)) {
                        echo '<div class="article-categories">';
                        foreach ($categories as $category) {
                            $level = nahda_get_category_level($category);
                            $icon = nahda_get_level_icon($level);
                            echo '<span class="category-tag level-' . $level . '">';
                            echo $icon . ' <a href="' . get_category_link($category->term_id) . '">' . $category->name . '</a>';
                            echo '</span>';
                        }
                        echo '</div>';
                    }
                    ?>
                </div>
                
                <h1 class="enhanced-article-title"><?php the_title(); ?></h1>
                
                <div class="article-meta-bottom">
                    <div class="meta-item">
                        <span class="meta-icon">📅</span>
                        <span class="meta-text">تاريخ النشر: <?php echo get_the_date('Y/m/d'); ?></span>
                    </div>
                    
                    <div class="meta-item">
                        <span class="meta-icon">📊</span>
                        <span class="meta-text">عدد الكلمات: <?php echo nahda_count_words(get_the_content()); ?></span>
                    </div>
                    
                    <div class="meta-item">
                        <span class="meta-icon">⏱️</span>
                        <span class="meta-text">وقت القراءة: <?php echo nahda_reading_time(get_the_content()); ?> دقيقة</span>
                    </div>
                </div>
            </header>
            
            <!-- جدول المحتويات التلقائي -->
            <?php 
            $content = get_the_content();
            if (str_word_count(strip_tags($content)) > 300) {
                echo nahda_generate_table_of_contents($content);
            }
            ?>
            
            <!-- محتوى المقال المحسن -->
            <div class="enhanced-article-content">
                <?php
                // عرض المحتوى مع التحسينات
                $enhanced_content = nahda_enhance_content_display($content);
                echo $enhanced_content;
                ?>
            </div>
            
            <!-- أدوات المقال -->
            <div class="article-tools">
                <div class="sharing-tools">
                    <h4>📤 مشاركة المقال</h4>
                    <div class="share-buttons">
                        <?php echo nahda_get_share_buttons(); ?>
                    </div>
                </div>
                
                <div class="navigation-tools">
                    <h4>🧭 التنقل</h4>
                    <div class="post-navigation">
                        <?php
                        $prev_post = get_previous_post();
                        $next_post = get_next_post();
                        
                        if ($prev_post) {
                            echo '<a href="' . get_permalink($prev_post->ID) . '" class="nav-link prev-post">';
                            echo '← المقال السابق: ' . get_the_title($prev_post->ID);
                            echo '</a>';
                        }
                        
                        if ($next_post) {
                            echo '<a href="' . get_permalink($next_post->ID) . '" class="nav-link next-post">';
                            echo 'المقال التالي: ' . get_the_title($next_post->ID) . ' →';
                            echo '</a>';
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <!-- مقالات ذات صلة -->
            <div class="related-articles">
                <h3>📚 مقالات ذات صلة</h3>
                <?php echo nahda_get_related_posts(); ?>
            </div>
            
        </article>
        
        <!-- شريط جانبي للمقال -->
        <aside class="article-sidebar">
            <!-- فهرس سريع للتصنيف -->
            <div class="sidebar-widget">
                <h4>📂 فهرس هذا القسم</h4>
                <?php
                $categories = get_the_category();
                if (!empty($categories)) {
                    $main_category = $categories[0];
                    echo nahda_get_category_quick_index($main_category->term_id);
                }
                ?>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="sidebar-widget">
                <h4>📊 إحصائيات سريعة</h4>
                <?php echo nahda_get_quick_stats(); ?>
            </div>
            
            <!-- أدوات مفيدة -->
            <div class="sidebar-widget">
                <h4>🔧 أدوات مفيدة</h4>
                <div class="useful-tools">
                    <button onclick="nahda_print_article()" class="tool-btn">🖨️ طباعة</button>
                    <button onclick="nahda_save_article()" class="tool-btn">💾 حفظ</button>
                    <button onclick="nahda_increase_font()" class="tool-btn">🔍 تكبير الخط</button>
                    <button onclick="nahda_decrease_font()" class="tool-btn">🔍 تصغير الخط</button>
                </div>
            </div>
        </aside>
        
    <?php endwhile; endif; ?>
</div>

<!-- أنماط CSS محسنة -->
<style>
.nahda-enhanced-post-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
    direction: rtl;
}

.nahda-enhanced-article {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    border-right: 5px solid #3498db;
}

.enhanced-article-header {
    border-bottom: 3px solid #ecf0f1;
    padding-bottom: 30px;
    margin-bottom: 40px;
}

.article-categories {
    margin-bottom: 20px;
}

.category-tag {
    display: inline-block;
    padding: 5px 12px;
    margin: 0 5px 5px 0;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
    text-decoration: none;
}

.category-tag.level-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.category-tag.level-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.category-tag.level-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.category-tag.level-4 {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.category-tag a {
    color: inherit;
    text-decoration: none;
}

.enhanced-article-title {
    font-size: 2.5em;
    color: #2c3e50;
    line-height: 1.3;
    margin: 20px 0;
    font-weight: 700;
}

.article-meta-bottom {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    color: #7f8c8d;
    font-size: 0.9em;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.enhanced-article-content {
    font-size: 18px;
    line-height: 1.8;
    color: #2c3e50;
    text-align: justify;
}

.enhanced-article-content p {
    margin-bottom: 1.5em;
}

.enhanced-article-content h2,
.enhanced-article-content h3,
.enhanced-article-content h4 {
    margin: 2em 0 1em 0;
    color: #34495e;
}

.article-tools {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #ecf0f1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.sharing-tools h4,
.navigation-tools h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.share-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.share-btn {
    padding: 8px 15px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 0.9em;
    font-weight: 600;
    transition: transform 0.2s ease;
}

.share-btn:hover {
    transform: translateY(-2px);
}

.post-navigation {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.nav-link {
    display: block;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #2c3e50;
    transition: background-color 0.2s ease;
}

.nav-link:hover {
    background: #e9ecef;
    color: #2c3e50;
}

.related-articles {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #ecf0f1;
}

.related-articles h3 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.article-sidebar {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.sidebar-widget {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.sidebar-widget:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.sidebar-widget h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.useful-tools {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.tool-btn {
    padding: 8px 12px;
    border: none;
    background: #3498db;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.85em;
    transition: background-color 0.2s ease;
}

.tool-btn:hover {
    background: #2980b9;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .nahda-enhanced-post-container {
        grid-template-columns: 1fr;
        padding: 10px;
    }
    
    .nahda-enhanced-article {
        padding: 20px;
    }
    
    .enhanced-article-title {
        font-size: 1.8em;
    }
    
    .enhanced-article-content {
        font-size: 16px;
    }
    
    .article-tools {
        grid-template-columns: 1fr;
    }
    
    .article-meta-bottom {
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<!-- JavaScript للأدوات -->
<script>
function nahda_print_article() {
    window.print();
}

function nahda_save_article() {
    // حفظ المقال في Local Storage
    const title = document.querySelector('.enhanced-article-title').textContent;
    const content = document.querySelector('.enhanced-article-content').innerHTML;
    const saved = {
        title: title,
        content: content,
        url: window.location.href,
        date: new Date().toISOString()
    };
    
    let savedArticles = JSON.parse(localStorage.getItem('nahda_saved_articles') || '[]');
    savedArticles.push(saved);
    localStorage.setItem('nahda_saved_articles', JSON.stringify(savedArticles));
    
    alert('تم حفظ المقال بنجاح!');
}

function nahda_increase_font() {
    const content = document.querySelector('.enhanced-article-content');
    const currentSize = parseInt(window.getComputedStyle(content).fontSize);
    content.style.fontSize = (currentSize + 2) + 'px';
}

function nahda_decrease_font() {
    const content = document.querySelector('.enhanced-article-content');
    const currentSize = parseInt(window.getComputedStyle(content).fontSize);
    if (currentSize > 14) {
        content.style.fontSize = (currentSize - 2) + 'px';
    }
}
</script>

<?php
// دوال مساعدة
function nahda_get_category_level($category) {
    $level = 1;
    $current = $category;
    
    while ($current->parent != 0) {
        $level++;
        $current = get_category($current->parent);
    }
    
    return $level;
}

function nahda_get_level_icon($level) {
    $icons = array(
        1 => '📚',
        2 => '📖',
        3 => '📝',
        4 => '🔸',
        5 => '📄'
    );
    
    return isset($icons[$level]) ? $icons[$level] : '•';
}

function nahda_count_words($content) {
    return str_word_count(strip_tags($content));
}

function nahda_reading_time($content) {
    $words = str_word_count(strip_tags($content));
    return ceil($words / 200); // 200 كلمة في الدقيقة
}

function nahda_enhance_content_display($content) {
    // تطبيق التحسينات من ملف fix_content_display.php
    $fixer = new NahdaContentFixer();
    return $fixer->format_full_content($content);
}

function nahda_generate_table_of_contents($content) {
    // إنشاء جدول المحتويات
    preg_match_all('/<h[2-4][^>]*>(.*?)<\/h[2-4]>/i', $content, $matches);
    
    if (empty($matches[1])) {
        return '';
    }
    
    $toc = '<div class="nahda-table-of-contents">';
    $toc .= '<h3>📋 محتويات المقال</h3>';
    $toc .= '<ul class="toc-list">';
    
    foreach ($matches[1] as $index => $heading) {
        $anchor = 'heading-' . ($index + 1);
        $clean_heading = strip_tags($heading);
        $toc .= '<li><a href="#' . $anchor . '">' . $clean_heading . '</a></li>';
    }
    
    $toc .= '</ul></div>';
    
    return $toc;
}

function nahda_get_share_buttons() {
    $url = get_permalink();
    $title = get_the_title();
    
    $buttons = '';
    $buttons .= '<a href="https://www.facebook.com/sharer/sharer.php?u=' . urlencode($url) . '" class="share-btn" style="background: #3b5998; color: white;">📘 فيسبوك</a>';
    $buttons .= '<a href="https://twitter.com/intent/tweet?url=' . urlencode($url) . '&text=' . urlencode($title) . '" class="share-btn" style="background: #1da1f2; color: white;">🐦 تويتر</a>';
    $buttons .= '<a href="https://wa.me/?text=' . urlencode($title . ' ' . $url) . '" class="share-btn" style="background: #25d366; color: white;">📱 واتساب</a>';
    
    return $buttons;
}

function nahda_get_related_posts() {
    $categories = get_the_category();
    if (empty($categories)) return '';
    
    $related = get_posts(array(
        'category__in' => array($categories[0]->term_id),
        'post__not_in' => array(get_the_ID()),
        'posts_per_page' => 5,
        'orderby' => 'rand'
    ));
    
    if (empty($related)) return '<p>لا توجد مقالات ذات صلة.</p>';
    
    $output = '<div class="related-posts-grid">';
    foreach ($related as $post) {
        $output .= '<div class="related-post-item">';
        $output .= '<h4><a href="' . get_permalink($post->ID) . '">' . get_the_title($post->ID) . '</a></h4>';
        $output .= '<p>' . wp_trim_words(get_the_excerpt($post->ID), 20) . '</p>';
        $output .= '</div>';
    }
    $output .= '</div>';
    
    return $output;
}

function nahda_get_category_quick_index($category_id) {
    $posts = get_posts(array(
        'category' => $category_id,
        'posts_per_page' => 10,
        'orderby' => 'date',
        'order' => 'ASC'
    ));
    
    if (empty($posts)) return '<p>لا توجد مقالات في هذا القسم.</p>';
    
    $output = '<ul class="quick-index">';
    foreach ($posts as $post) {
        $current = (get_the_ID() == $post->ID) ? ' class="current"' : '';
        $output .= '<li' . $current . '><a href="' . get_permalink($post->ID) . '">' . get_the_title($post->ID) . '</a></li>';
    }
    $output .= '</ul>';
    
    return $output;
}

function nahda_get_quick_stats() {
    $categories = get_the_category();
    $category_id = !empty($categories) ? $categories[0]->term_id : 0;
    
    $stats = '';
    $stats .= '<div class="stat-item">📄 المقالات في هذا القسم: ' . get_category($category_id)->count . '</div>';
    $stats .= '<div class="stat-item">📅 تاريخ النشر: ' . get_the_date('Y/m/d') . '</div>';
    $stats .= '<div class="stat-item">👁️ المشاهدات: ' . (get_post_meta(get_the_ID(), 'views', true) ?: '0') . '</div>';
    
    return $stats;
}

get_footer(); ?>
