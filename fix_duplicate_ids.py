#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Duplicate IDs in SQL Files
Resolves PRIMARY KEY conflicts during import
"""

import re
import os

def fix_sql_file(filename):
    """Fix duplicate IDs in SQL file"""
    print(f"🔧 إصلاح الـ IDs المكررة في: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the highest existing post ID in WordPress
        # We'll start from ID 10000 to avoid conflicts
        starting_id = 10000
        
        # Pattern to match INSERT INTO wp_posts with ID
        pattern = r'INSERT INTO wp_posts \([^)]+\) VALUES \(\s*(\d+),'
        
        def replace_id(match):
            nonlocal starting_id
            old_id = match.group(1)
            new_content = match.group(0).replace(f'({old_id},', f'({starting_id},')
            starting_id += 1
            return new_content
        
        # Replace all post IDs
        fixed_content = re.sub(pattern, replace_id, content)
        
        # Also fix the term_relationships references
        # Pattern for term_relationships
        rel_pattern = r'INSERT INTO wp_term_relationships \(object_id, term_taxonomy_id, term_order\) VALUES \((\d+),'
        
        # Create mapping of old IDs to new IDs
        old_ids = re.findall(r'INSERT INTO wp_posts \([^)]+\) VALUES \(\s*(\d+),', content)
        new_ids = list(range(10000, 10000 + len(old_ids)))
        id_mapping = dict(zip(old_ids, new_ids))
        
        def replace_rel_id(match):
            old_id = match.group(1)
            if old_id in id_mapping:
                new_id = id_mapping[old_id]
                return match.group(0).replace(f'({old_id},', f'({new_id},')
            return match.group(0)
        
        # Fix term_relationships
        fixed_content = re.sub(rel_pattern, replace_rel_id, fixed_content)
        
        # Save fixed file
        fixed_filename = filename.replace('.sql', '_fixed.sql')
        with open(fixed_filename, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ تم إصلاح الملف: {fixed_filename}")
        print(f"   📊 تم تغيير {len(old_ids)} ID")
        print(f"   🆔 الـ IDs الجديدة تبدأ من: {10000}")
        
        return fixed_filename
    
    except Exception as e:
        print(f"❌ خطأ في إصلاح الملف: {e}")
        return None

def create_clean_import_sql():
    """Create clean SQL that avoids ID conflicts"""
    print("🧹 إنشاء ملف SQL نظيف بدون تعارض IDs...")
    
    clean_sql = """-- Clean WordPress Import - No ID Conflicts
-- This file safely imports without PRIMARY KEY conflicts
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing data (OPTIONAL - remove if you want to keep existing content)
-- DELETE FROM wp_term_relationships WHERE term_taxonomy_id > 1;
-- DELETE FROM wp_posts WHERE ID > 1;
-- DELETE FROM wp_term_taxonomy WHERE term_taxonomy_id > 1;
-- DELETE FROM wp_terms WHERE term_id > 1;

-- Reset AUTO_INCREMENT to start from safe numbers
ALTER TABLE wp_posts AUTO_INCREMENT = 10000;
ALTER TABLE wp_terms AUTO_INCREMENT = 1000;
ALTER TABLE wp_term_taxonomy AUTO_INCREMENT = 1000;

-- Now import your content using the fixed files
-- Source the fixed SQL files in order:
-- SOURCE 01_categories_fixed.sql;
-- SOURCE 02_posts_fixed.sql;
-- ... etc

SET FOREIGN_KEY_CHECKS = 1;

-- Update WordPress options
UPDATE wp_options 
SET option_value = 'مشروع النهضة وبناء الدولة السورية' 
WHERE option_name = 'blogname';

UPDATE wp_options 
SET option_value = 'كتاب شامل لمشروع النهضة السورية - ما بعد الاستبداد' 
WHERE option_name = 'blogdescription';

UPDATE wp_options 
SET option_value = 'ar' 
WHERE option_name = 'WPLANG';

-- Update category counts
UPDATE wp_term_taxonomy tt
SET count = (
    SELECT COUNT(*) 
    FROM wp_term_relationships tr 
    WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
)
WHERE tt.taxonomy = 'category';

-- End of clean import"""
    
    with open('00_clean_import.sql', 'w', encoding='utf-8') as f:
        f.write(clean_sql)
    
    print("✅ تم إنشاء: 00_clean_import.sql")

def create_safe_test_version():
    """Create a safe test version that won't conflict"""
    print("🧪 إنشاء نسخة اختبار آمنة...")
    
    safe_sql = """-- Safe Test Import for Nahda Book
-- Uses high IDs to avoid conflicts
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Create test categories starting from ID 1000
INSERT INTO wp_terms (term_id, name, slug, term_group) VALUES 
(1000, 'مشروع النهضة', 'nahda-project', 0),
(1001, 'الباب الأول - الأسس النظرية', 'theoretical-foundations', 0),
(1002, 'الباب الثاني - البناء السياسي', 'political-construction', 0),
(1003, 'الباب الثالث - التنمية الاقتصادية', 'economic-development', 0),
(1004, 'الباب الرابع - الإصلاح الاجتماعي', 'social-reform', 0);

-- Create term taxonomy
INSERT INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) VALUES 
(1000, 1000, 'category', 'التصنيف الرئيسي لمشروع النهضة', 0, 4),
(1001, 1001, 'category', 'الأسس النظرية للمشروع', 1000, 1),
(1002, 1002, 'category', 'البناء السياسي والدستوري', 1000, 1),
(1003, 1003, 'category', 'التنمية والاقتصاد', 1000, 1),
(1004, 1004, 'category', 'الإصلاح الاجتماعي', 1000, 1);

-- Create test posts starting from ID 10000
INSERT INTO wp_posts (
    ID, post_author, post_date, post_date_gmt, post_content, post_title,
    post_excerpt, post_status, comment_status, ping_status, post_password,
    post_name, to_ping, pinged, post_modified, post_modified_gmt,
    post_content_filtered, post_parent, guid, menu_order, post_type,
    post_mime_type, comment_count
) VALUES 
(10000, 1, NOW(), NOW(), 
'مقدمة شاملة لمشروع النهضة السورية تتناول الأسس النظرية والفكرية للمشروع، وتحدد الإطار العام للتغيير المطلوب في سورية ما بعد الاستبداد. يركز هذا القسم على بناء رؤية متكاملة للدولة الحديثة.',
'مقدمة مشروع النهضة السورية',
'مقدمة شاملة لمشروع النهضة السورية وأسسه النظرية',
'publish', 'open', 'open', '', 'nahda-introduction',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10001, 1, NOW(), NOW(),
'تحليل معمق للأسس النظرية التي يقوم عليها مشروع النهضة، بما في ذلك المبادئ الدستورية والقانونية والفلسفية التي تحكم بناء الدولة الحديثة في سورية.',
'الأسس النظرية لمشروع النهضة',
'تحليل الأسس النظرية والفلسفية للمشروع',
'publish', 'open', 'open', '', 'theoretical-foundations',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10002, 1, NOW(), NOW(),
'استراتيجية شاملة للبناء السياسي في سورية الجديدة، تشمل النظام السياسي، والدستور، والمؤسسات، وآليات الحكم الديمقراطي والمشاركة الشعبية.',
'استراتيجية البناء السياسي',
'خطة شاملة للبناء السياسي والدستوري',
'publish', 'open', 'open', '', 'political-construction-strategy',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10003, 1, NOW(), NOW(),
'رؤية اقتصادية متكاملة للتنمية في سورية ما بعد الحرب، تركز على إعادة الإعمار، والتنمية المستدامة، والعدالة الاجتماعية في التوزيع.',
'رؤية التنمية الاقتصادية',
'استراتيجية التنمية الاقتصادية المستدامة',
'publish', 'open', 'open', '', 'economic-development-vision',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10004, 1, NOW(), NOW(),
'برنامج شامل للإصلاح الاجتماعي يهدف إلى بناء مجتمع عادل ومتماسك، يركز على التعليم، والصحة، والعدالة الاجتماعية، وحقوق الإنسان.',
'برنامج الإصلاح الاجتماعي',
'خطة متكاملة للإصلاح الاجتماعي والثقافي',
'publish', 'open', 'open', '', 'social-reform-program',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0);

-- Link posts to categories
INSERT INTO wp_term_relationships (object_id, term_taxonomy_id, term_order) VALUES 
(10000, 1000, 0),
(10001, 1001, 0),
(10002, 1002, 0),
(10003, 1003, 0),
(10004, 1004, 0);

-- Update category counts
UPDATE wp_term_taxonomy SET count = 1 WHERE term_taxonomy_id IN (1001, 1002, 1003, 1004);
UPDATE wp_term_taxonomy SET count = 4 WHERE term_taxonomy_id = 1000;

-- Update WordPress settings
UPDATE wp_options SET option_value = 'مشروع النهضة - نسخة تجريبية' WHERE option_name = 'blogname';
UPDATE wp_options SET option_value = 'نسخة تجريبية من مشروع النهضة السورية' WHERE option_name = 'blogdescription';

SET FOREIGN_KEY_CHECKS = 1;

-- Success message
SELECT 'تم استيراد نسخة الاختبار بنجاح! تحقق من المقالات والتصنيفات في لوحة التحكم.' as 'رسالة النجاح';"""
    
    with open('safe_test_import.sql', 'w', encoding='utf-8') as f:
        f.write(safe_sql)
    
    print("✅ تم إنشاء: safe_test_import.sql")

def main():
    """Main function"""
    print("🛠️ إصلاح مشكلة الـ IDs المكررة")
    print("=" * 50)
    
    # Create safe versions
    create_clean_import_sql()
    create_safe_test_version()
    
    # Fix existing files if they exist
    sql_files = [f for f in os.listdir('.') if f.endswith('.sql') and not f.endswith('_fixed.sql')]
    
    print(f"\n🔍 العثور على {len(sql_files)} ملف SQL")
    
    choice = input("\n❓ هل تريد إصلاح الملفات الموجودة؟ (y/n): ").strip().lower()
    
    if choice == 'y':
        for sql_file in sql_files:
            if sql_file.startswith(('01_', '02_', '03_', '04_', '05_', '06_', '07_', '08_', '09_', '10_', '11_')):
                fix_sql_file(sql_file)
    
    print("\n" + "=" * 50)
    print("✅ تم إنشاء الحلول التالية:")
    print("   🧪 safe_test_import.sql - نسخة اختبار آمنة")
    print("   🧹 00_clean_import.sql - إعداد نظيف للاستيراد")
    
    if choice == 'y':
        print("   🔧 ملفات *_fixed.sql - نسخ مُصححة من الملفات الأصلية")
    
    print(f"\n🎯 التوصية:")
    print(f"   1. جرب safe_test_import.sql أولاً")
    print(f"   2. إذا نجح، استخدم 00_clean_import.sql ثم الملفات المُصححة")
    print(f"   3. أو احذف المحتوى الموجود واستورد من جديد")

if __name__ == "__main__":
    main()
