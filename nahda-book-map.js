/**
 * خارطة كتاب النهضة - JavaScript للتفاعل
 */

jQuery(document).ready(function($) {
    
    // تفعيل الشجرة القابلة للطي
    initTreeToggle();
    
    // تفعيل البحث السريع
    initQuickSearch();
    
    // تفعيل الأكورديون
    initAccordion();
    
    // تفعيل التمرير السلس
    initSmoothScroll();
    
    /**
     * تفعيل الشجرة القابلة للطي
     */
    function initTreeToggle() {
        $('.toggle-btn').on('click', function() {
            var targetId = $(this).data('target');
            var target = $('#' + targetId);
            
            if (target.is(':visible')) {
                target.slideUp(300);
                $(this).text('▶');
            } else {
                target.slideDown(300);
                $(this).text('▼');
            }
        });
        
        // طي جميع المستويات العميقة افتراضياً
        $('.book-tree .level-3 .children, .book-tree .level-4 .children').hide();
        $('.book-tree .level-3 .toggle-btn, .book-tree .level-4 .toggle-btn').text('▶');
    }
    
    /**
     * تفعيل البحث السريع
     */
    function initQuickSearch() {
        var searchTimeout;
        
        $('#book-search').on('input', function() {
            var query = $(this).val().trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length < 2) {
                $('#search-results').empty();
                return;
            }
            
            searchTimeout = setTimeout(function() {
                performSearch(query);
            }, 300);
        });
    }
    
    /**
     * تنفيذ البحث
     */
    function performSearch(query) {
        $('#search-results').html('<div class="search-loading">🔍 جاري البحث...</div>');
        
        // البحث في التصنيفات
        var categoryResults = searchInCategories(query);
        
        // البحث في المقالات (محاكاة - يمكن تطويرها مع AJAX)
        var postResults = searchInPosts(query);
        
        displaySearchResults(categoryResults, postResults);
    }
    
    /**
     * البحث في التصنيفات
     */
    function searchInCategories(query) {
        var results = [];
        
        $('.category-link').each(function() {
            var categoryName = $(this).text();
            if (categoryName.toLowerCase().includes(query.toLowerCase())) {
                results.push({
                    type: 'category',
                    title: categoryName,
                    url: $(this).attr('href'),
                    level: $(this).closest('.tree-item').attr('class').match(/level-(\d+)/)[1]
                });
            }
        });
        
        return results;
    }
    
    /**
     * البحث في المقالات (محاكاة)
     */
    function searchInPosts(query) {
        // هذه دالة محاكاة - يمكن تطويرها مع AJAX للبحث الفعلي
        return [];
    }
    
    /**
     * عرض نتائج البحث
     */
    function displaySearchResults(categoryResults, postResults) {
        var resultsHtml = '';
        
        if (categoryResults.length === 0 && postResults.length === 0) {
            resultsHtml = '<div class="no-results">❌ لم يتم العثور على نتائج</div>';
        } else {
            if (categoryResults.length > 0) {
                resultsHtml += '<h4>📂 التصنيفات:</h4>';
                resultsHtml += '<ul class="search-results-list">';
                
                categoryResults.slice(0, 10).forEach(function(result) {
                    var levelIcon = getLevelIcon(result.level);
                    resultsHtml += '<li class="search-result-item">';
                    resultsHtml += '<span class="result-icon">' + levelIcon + '</span>';
                    resultsHtml += '<a href="' + result.url + '">' + result.title + '</a>';
                    resultsHtml += '</li>';
                });
                
                resultsHtml += '</ul>';
                
                if (categoryResults.length > 10) {
                    resultsHtml += '<div class="more-results">... و ' + (categoryResults.length - 10) + ' نتائج أخرى</div>';
                }
            }
            
            if (postResults.length > 0) {
                resultsHtml += '<h4>📄 المقالات:</h4>';
                resultsHtml += '<ul class="search-results-list">';
                
                postResults.slice(0, 10).forEach(function(result) {
                    resultsHtml += '<li class="search-result-item">';
                    resultsHtml += '<a href="' + result.url + '">' + result.title + '</a>';
                    resultsHtml += '</li>';
                });
                
                resultsHtml += '</ul>';
            }
        }
        
        $('#search-results').html(resultsHtml);
    }
    
    /**
     * الحصول على أيقونة المستوى
     */
    function getLevelIcon(level) {
        var icons = {
            '1': '📚',
            '2': '📖',
            '3': '📝',
            '4': '🔸',
            '5': '📄'
        };
        
        return icons[level] || '•';
    }
    
    /**
     * تفعيل الأكورديون
     */
    function initAccordion() {
        $('.accordion-header').on('click', function() {
            var content = $(this).next('.accordion-content');
            var isActive = $(this).hasClass('active');
            
            // إغلاق جميع الأقسام الأخرى
            $('.accordion-header').removeClass('active');
            $('.accordion-content').removeClass('active').slideUp(300);
            
            if (!isActive) {
                $(this).addClass('active');
                content.addClass('active').slideDown(300);
            }
        });
    }
    
    /**
     * تفعيل التمرير السلس
     */
    function initSmoothScroll() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            var target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });
    }
    
    /**
     * إضافة وظائف إضافية
     */
    
    // توسيع/طي جميع العقد
    if ($('.tree-map').length) {
        var controlsHtml = '<div class="tree-controls">';
        controlsHtml += '<button id="expand-all" class="tree-control-btn">📖 توسيع الكل</button>';
        controlsHtml += '<button id="collapse-all" class="tree-control-btn">📚 طي الكل</button>';
        controlsHtml += '</div>';
        
        $('.tree-map h3').after(controlsHtml);
        
        $('#expand-all').on('click', function() {
            $('.children').slideDown(300);
            $('.toggle-btn').text('▼');
        });
        
        $('#collapse-all').on('click', function() {
            $('.children').slideUp(300);
            $('.toggle-btn').text('▶');
        });
    }
    
    // إحصائيات ديناميكية
    updateDynamicStats();
    
    /**
     * تحديث الإحصائيات الديناميكية
     */
    function updateDynamicStats() {
        // عدد التصنيفات المرئية
        var visibleCategories = $('.category-link').length;
        
        // إضافة معلومات إضافية
        if ($('.book-statistics').length) {
            var additionalInfo = '<div class="additional-stats">';
            additionalInfo += '<small>📊 إجمالي التصنيفات المعروضة: ' + visibleCategories + '</small>';
            additionalInfo += '</div>';
            
            $('.book-statistics').append(additionalInfo);
        }
    }
    
    // تفعيل التلميحات (Tooltips)
    initTooltips();
    
    /**
     * تفعيل التلميحات
     */
    function initTooltips() {
        $('.category-link').each(function() {
            var $this = $(this);
            var level = $this.closest('.tree-item').attr('class').match(/level-(\d+)/)[1];
            var levelName = getLevelName(level);
            
            $this.attr('title', levelName + ': ' + $this.text());
        });
    }
    
    /**
     * الحصول على اسم المستوى
     */
    function getLevelName(level) {
        var names = {
            '1': 'قسم',
            '2': 'باب',
            '3': 'فصل',
            '4': 'مبحث',
            '5': 'مقال'
        };
        
        return names[level] || 'مستوى ' + level;
    }
    
    // تحسين الأداء - تحميل كسول للمحتوى
    initLazyLoading();
    
    /**
     * تحميل كسول للمحتوى
     */
    function initLazyLoading() {
        // إخفاء المستويات العميقة افتراضياً لتحسين الأداء
        $('.level-4, .level-5').hide();
        
        // إظهارها عند الحاجة
        $('.level-3 .toggle-btn').on('click', function() {
            var targetId = $(this).data('target');
            $('#' + targetId + ' .level-4, #' + targetId + ' .level-5').show();
        });
    }
    
    // إضافة مؤشر التحميل
    function showLoading(element) {
        element.addClass('loading');
    }
    
    function hideLoading(element) {
        element.removeClass('loading');
    }
    
    // تصدير الوظائف للاستخدام الخارجي
    window.NahdaBookMap = {
        expandAll: function() {
            $('#expand-all').click();
        },
        collapseAll: function() {
            $('#collapse-all').click();
        },
        search: function(query) {
            $('#book-search').val(query).trigger('input');
        }
    };
});

// CSS إضافي للعناصر الجديدة
var additionalCSS = `
.tree-controls {
    margin-bottom: 20px;
    text-align: center;
}

.tree-control-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    margin: 0 5px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.tree-control-btn:hover {
    background: #2980b9;
}

.search-results-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.search-result-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
}

.search-result-item:hover {
    background: #f8f9fa;
}

.result-icon {
    margin-left: 8px;
    font-size: 14px;
}

.search-result-item a {
    text-decoration: none;
    color: #2c3e50;
    flex-grow: 1;
}

.search-result-item a:hover {
    color: #3498db;
}

.no-results {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.search-loading {
    text-align: center;
    padding: 10px;
    color: #6c757d;
}

.more-results {
    text-align: center;
    padding: 10px;
    color: #6c757d;
    font-style: italic;
}

.additional-stats {
    margin-top: 15px;
    text-align: center;
    opacity: 0.8;
}
`;

// إضافة CSS إضافي
if (!document.getElementById('nahda-additional-css')) {
    var style = document.createElement('style');
    style.id = 'nahda-additional-css';
    style.textContent = additionalCSS;
    document.head.appendChild(style);
}
