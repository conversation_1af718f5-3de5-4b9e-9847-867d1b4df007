-- Safe Test Import for Nahda Book
-- Uses high IDs to avoid conflicts
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Create test categories starting from ID 1000
INSERT INTO wp_terms (term_id, name, slug, term_group) VALUES 
(1000, 'مشروع النهضة', 'nahda-project', 0),
(1001, 'الباب الأول - الأسس النظرية', 'theoretical-foundations', 0),
(1002, 'الباب الثاني - البناء السياسي', 'political-construction', 0),
(1003, 'الباب الثالث - التنمية الاقتصادية', 'economic-development', 0),
(1004, 'الباب الرابع - الإصلاح الاجتماعي', 'social-reform', 0);

-- Create term taxonomy
INSERT INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) VALUES 
(1000, 1000, 'category', 'التصنيف الرئيسي لمشروع النهضة', 0, 4),
(1001, 1001, 'category', 'الأسس النظرية للمشروع', 1000, 1),
(1002, 1002, 'category', 'البناء السياسي والدستوري', 1000, 1),
(1003, 1003, 'category', 'التنمية والاقتصاد', 1000, 1),
(1004, 1004, 'category', 'الإصلاح الاجتماعي', 1000, 1);

-- Create test posts starting from ID 10000
INSERT INTO wp_posts (
    ID, post_author, post_date, post_date_gmt, post_content, post_title,
    post_excerpt, post_status, comment_status, ping_status, post_password,
    post_name, to_ping, pinged, post_modified, post_modified_gmt,
    post_content_filtered, post_parent, guid, menu_order, post_type,
    post_mime_type, comment_count
) VALUES 
(10000, 1, NOW(), NOW(), 
'مقدمة شاملة لمشروع النهضة السورية تتناول الأسس النظرية والفكرية للمشروع، وتحدد الإطار العام للتغيير المطلوب في سورية ما بعد الاستبداد. يركز هذا القسم على بناء رؤية متكاملة للدولة الحديثة.',
'مقدمة مشروع النهضة السورية',
'مقدمة شاملة لمشروع النهضة السورية وأسسه النظرية',
'publish', 'open', 'open', '', 'nahda-introduction',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10001, 1, NOW(), NOW(),
'تحليل معمق للأسس النظرية التي يقوم عليها مشروع النهضة، بما في ذلك المبادئ الدستورية والقانونية والفلسفية التي تحكم بناء الدولة الحديثة في سورية.',
'الأسس النظرية لمشروع النهضة',
'تحليل الأسس النظرية والفلسفية للمشروع',
'publish', 'open', 'open', '', 'theoretical-foundations',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10002, 1, NOW(), NOW(),
'استراتيجية شاملة للبناء السياسي في سورية الجديدة، تشمل النظام السياسي، والدستور، والمؤسسات، وآليات الحكم الديمقراطي والمشاركة الشعبية.',
'استراتيجية البناء السياسي',
'خطة شاملة للبناء السياسي والدستوري',
'publish', 'open', 'open', '', 'political-construction-strategy',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10003, 1, NOW(), NOW(),
'رؤية اقتصادية متكاملة للتنمية في سورية ما بعد الحرب، تركز على إعادة الإعمار، والتنمية المستدامة، والعدالة الاجتماعية في التوزيع.',
'رؤية التنمية الاقتصادية',
'استراتيجية التنمية الاقتصادية المستدامة',
'publish', 'open', 'open', '', 'economic-development-vision',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0),

(10004, 1, NOW(), NOW(),
'برنامج شامل للإصلاح الاجتماعي يهدف إلى بناء مجتمع عادل ومتماسك، يركز على التعليم، والصحة، والعدالة الاجتماعية، وحقوق الإنسان.',
'برنامج الإصلاح الاجتماعي',
'خطة متكاملة للإصلاح الاجتماعي والثقافي',
'publish', 'open', 'open', '', 'social-reform-program',
'', '', NOW(), NOW(), '', 0, '', 0, 'post', '', 0);

-- Link posts to categories
INSERT INTO wp_term_relationships (object_id, term_taxonomy_id, term_order) VALUES 
(10000, 1000, 0),
(10001, 1001, 0),
(10002, 1002, 0),
(10003, 1003, 0),
(10004, 1004, 0);

-- Update category counts
UPDATE wp_term_taxonomy SET count = 1 WHERE term_taxonomy_id IN (1001, 1002, 1003, 1004);
UPDATE wp_term_taxonomy SET count = 4 WHERE term_taxonomy_id = 1000;

-- Update WordPress settings
UPDATE wp_options SET option_value = 'مشروع النهضة - نسخة تجريبية' WHERE option_name = 'blogname';
UPDATE wp_options SET option_value = 'نسخة تجريبية من مشروع النهضة السورية' WHERE option_name = 'blogdescription';

SET FOREIGN_KEY_CHECKS = 1;

-- Success message
SELECT 'تم استيراد نسخة الاختبار بنجاح! تحقق من المقالات والتصنيفات في لوحة التحكم.' as 'رسالة النجاح';