docx/__init__.py,sha256=UMByK5jqZm7_cPIn3H_u8b2JyDVZHQXidPsaRYt0dyI,1711
docx/__pycache__/__init__.cpython-313.pyc,,
docx/__pycache__/api.cpython-313.pyc,,
docx/__pycache__/blkcntnr.cpython-313.pyc,,
docx/__pycache__/document.cpython-313.pyc,,
docx/__pycache__/exceptions.cpython-313.pyc,,
docx/__pycache__/package.cpython-313.pyc,,
docx/__pycache__/section.cpython-313.pyc,,
docx/__pycache__/settings.cpython-313.pyc,,
docx/__pycache__/shape.cpython-313.pyc,,
docx/__pycache__/shared.cpython-313.pyc,,
docx/__pycache__/table.cpython-313.pyc,,
docx/__pycache__/types.cpython-313.pyc,,
docx/api.py,sha256=13U56iPSM2tMRDEb03OmSZQkDAPmwGjfxosYkWS2tcc,1350
docx/blkcntnr.py,sha256=x9xGGKHwP4i2bpmRP7QNlAptnv5n_HNmncqmmFCck-4,3469
docx/dml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/dml/__pycache__/__init__.cpython-313.pyc,,
docx/dml/__pycache__/color.cpython-313.pyc,,
docx/dml/color.py,sha256=YBnwrKniDuxUgutlL9ZdVPwXNnAz46MK0OXFWCbpKQs,3794
docx/document.py,sha256=bLa3hEWOco16VtzC0FZW7ZP9R8uyDcmPOSRrJlK6YLs,8021
docx/drawing/__init__.py,sha256=_NrpR-TVKp5CnRE7rY53XBd_inyfOJg4t0BgQoYEbB8,504
docx/drawing/__pycache__/__init__.cpython-313.pyc,,
docx/enum/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/enum/__pycache__/__init__.cpython-313.pyc,,
docx/enum/__pycache__/base.cpython-313.pyc,,
docx/enum/__pycache__/dml.cpython-313.pyc,,
docx/enum/__pycache__/section.cpython-313.pyc,,
docx/enum/__pycache__/shape.cpython-313.pyc,,
docx/enum/__pycache__/style.cpython-313.pyc,,
docx/enum/__pycache__/table.cpython-313.pyc,,
docx/enum/__pycache__/text.cpython-313.pyc,,
docx/enum/base.py,sha256=k1DRCRrFiXTBUnhAz_PK8uH7eDIy020n61C2hny58FQ,4888
docx/enum/dml.py,sha256=DqqkYxl09iXQgDNZxxl_wi_n9rA7mTokde1TpO3ygmU,3346
docx/enum/section.py,sha256=mNN1kF1mdIJQ82XFFZnd3yXXnCu5mfnXzopalnmPCqo,2391
docx/enum/shape.py,sha256=DKQm6J3Is2050bT5RseUpwPHZZl9at0OAQjU1ha2H4g,396
docx/enum/style.py,sha256=Z9cBR7N3mCmZ54nKJxKqPhR4SMy_hqJRH2yabDxRLo8,10137
docx/enum/table.py,sha256=7PuQ--PGnSaNiE0Bu4VJ9t6LOgM-hNNmKEcl-3S2eUk,3943
docx/enum/text.py,sha256=o6bdpJot2iY40UnnBQ1uGfdE8oHFm3dtXUrmt9eMlvE,10276
docx/exceptions.py,sha256=WWiMy3ZtsfKRS31Wv09egLZnpPkX0H66B8JkOPCC3FM,454
docx/image/__init__.py,sha256=dSPABvSJkEyZO-Ov68OzLRWTKsXdv8x6Q3qhP0N3KiQ,664
docx/image/__pycache__/__init__.cpython-313.pyc,,
docx/image/__pycache__/bmp.cpython-313.pyc,,
docx/image/__pycache__/constants.cpython-313.pyc,,
docx/image/__pycache__/exceptions.cpython-313.pyc,,
docx/image/__pycache__/gif.cpython-313.pyc,,
docx/image/__pycache__/helpers.cpython-313.pyc,,
docx/image/__pycache__/image.cpython-313.pyc,,
docx/image/__pycache__/jpeg.cpython-313.pyc,,
docx/image/__pycache__/png.cpython-313.pyc,,
docx/image/__pycache__/tiff.cpython-313.pyc,,
docx/image/bmp.py,sha256=w_Jhu6hByAckJNyKhI3zHOKsHyibHgxDIjiPI9ev704,1347
docx/image/constants.py,sha256=W2MZsCZXV2-XYrI8ZaChsUQKcPmoSuCFvtepEntfX6Q,3466
docx/image/exceptions.py,sha256=_6rkjXXcbFxb_H-J0kK4Q1J9RaLx3TVQ5hoOVENvQaI,383
docx/image/gif.py,sha256=0R7L0r7kjf844pdpN6UKEISsEQzOPAyA3cghXXdJW2s,1118
docx/image/helpers.py,sha256=jy0fQBqIfstp9ExfKw3dGNKozn74RnmSA_YwmolsDqM,3089
docx/image/image.py,sha256=9TAFLP4tKqxcVizO_DjBv9tvOWXdlee0EJcKuurZ1xg,8011
docx/image/jpeg.py,sha256=jKYDYIiVYVq-DnVfh6nE-ZohWIBOEpnylDnVTUI7lak,15422
docx/image/png.py,sha256=hGpF7Nr7rBUCkE9NoHtQIrKM8hpiBaJXWio28vfZs0U,8214
docx/image/tiff.py,sha256=LZx-P2uMSzgdzHfIWRqRqtnbhuIqWBY_cLkx9_HTrPs,10379
docx/opc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/opc/__pycache__/__init__.cpython-313.pyc,,
docx/opc/__pycache__/constants.cpython-313.pyc,,
docx/opc/__pycache__/coreprops.cpython-313.pyc,,
docx/opc/__pycache__/exceptions.cpython-313.pyc,,
docx/opc/__pycache__/oxml.cpython-313.pyc,,
docx/opc/__pycache__/package.cpython-313.pyc,,
docx/opc/__pycache__/packuri.cpython-313.pyc,,
docx/opc/__pycache__/part.cpython-313.pyc,,
docx/opc/__pycache__/phys_pkg.cpython-313.pyc,,
docx/opc/__pycache__/pkgreader.cpython-313.pyc,,
docx/opc/__pycache__/pkgwriter.cpython-313.pyc,,
docx/opc/__pycache__/rel.cpython-313.pyc,,
docx/opc/__pycache__/shared.cpython-313.pyc,,
docx/opc/__pycache__/spec.cpython-313.pyc,,
docx/opc/constants.py,sha256=nY5tv7baqbqPTap65IonY-0ZiqavbsBxLl6a9XXxHxU,19080
docx/opc/coreprops.py,sha256=Bawgjt98BoPjUH69dqAoTCOZV3eJt9A4tASLukKyTtI,3402
docx/opc/exceptions.py,sha256=pqa1GuFxMlGZDBgGBqntZ_8aDINLwo2gtPuNRwADzyM,264
docx/opc/oxml.py,sha256=yRVsn5KZvsv3VZ_GhdI800_NNQ1qMGMK8oDatJO2OnY,8238
docx/opc/package.py,sha256=_XT-6j_cA8sSytKPLFcagFydWb8-pakeYRtXYLAyTag,8761
docx/opc/packuri.py,sha256=JOYwi5eUDIFwAWwvkWZkEZm6MzTvb6qIty--0RRbkOI,3733
docx/opc/part.py,sha256=vi0T3JiG1SUknq5aQdNL9ZJSnDY8B3832diSR_maSJ4,9025
docx/opc/parts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/opc/parts/__pycache__/__init__.cpython-313.pyc,,
docx/opc/parts/__pycache__/coreprops.cpython-313.pyc,,
docx/opc/parts/coreprops.py,sha256=VBczdH-CIj-tcE4bB11LxwmZkFGM2AG1BVyiJ2A2mCY,1775
docx/opc/phys_pkg.py,sha256=-eNZDBbLShHo3W1iglf1CwN_nkK8qnmTWp6ABg77cjA,4005
docx/opc/pkgreader.py,sha256=7EeCWbu7q_BA7huAG78k4WNR6vqfF7GopOBf5-K6NWE,9640
docx/opc/pkgwriter.py,sha256=shDeuBtW2BbFOTHrXx8R97PWFaH_fE37JYBfbw6QWRs,4524
docx/opc/rel.py,sha256=d-jNh9fpbti3SwzprbM2Y5hLjEUIykSfqw1iIdWsLfo,5653
docx/opc/shared.py,sha256=Zn2avg2g0J2fkG5YgFfCbl138gXex8stR-QwVJcuSIY,996
docx/opc/spec.py,sha256=GP8YEb7bSmZQ0i338M3lQCVZm6yUnkUhm3PFKVgW8bQ,638
docx/oxml/__init__.py,sha256=CC1sea239Nnj6sbQV0zT1YL2Vy-JinzzcnO0WqXlweA,8100
docx/oxml/__pycache__/__init__.cpython-313.pyc,,
docx/oxml/__pycache__/coreprops.cpython-313.pyc,,
docx/oxml/__pycache__/document.cpython-313.pyc,,
docx/oxml/__pycache__/drawing.cpython-313.pyc,,
docx/oxml/__pycache__/exceptions.cpython-313.pyc,,
docx/oxml/__pycache__/ns.cpython-313.pyc,,
docx/oxml/__pycache__/numbering.cpython-313.pyc,,
docx/oxml/__pycache__/parser.cpython-313.pyc,,
docx/oxml/__pycache__/section.cpython-313.pyc,,
docx/oxml/__pycache__/settings.cpython-313.pyc,,
docx/oxml/__pycache__/shape.cpython-313.pyc,,
docx/oxml/__pycache__/shared.cpython-313.pyc,,
docx/oxml/__pycache__/simpletypes.cpython-313.pyc,,
docx/oxml/__pycache__/styles.cpython-313.pyc,,
docx/oxml/__pycache__/table.cpython-313.pyc,,
docx/oxml/__pycache__/xmlchemy.cpython-313.pyc,,
docx/oxml/coreprops.py,sha256=q8LsifUyvD4Y9UabnXePVVnCDwn3yQo57mX_Au6pZ6k,10594
docx/oxml/document.py,sha256=Dri6Za3_9F14uVgoP-Es2VOisixPrqBeu6QR_AqeriA,3593
docx/oxml/drawing.py,sha256=JkqiKq2HmhdC8qWX_pavEIsjmBb2ZrBkoVGnSEstSig,397
docx/oxml/exceptions.py,sha256=v4USEteepc5EldWkA2jhb503rLTDLCr8ZdR1IbIKMdc,261
docx/oxml/ns.py,sha256=RO7gIZNT0z9_w-ShEQcFUe40TlQEK3ZDEEgzQHJyE9w,3796
docx/oxml/numbering.py,sha256=_LDiz7xbTI-UUnfBZSH5tQ76f4ju2m1eHnwfCJ9-PCw,3965
docx/oxml/parser.py,sha256=Zm53GoWo6KvIYIyq64s8kny_VzUoozvGrq6WsWUTqxU,2324
docx/oxml/section.py,sha256=ny6fhO5p4XE16CjmXgsV2f_LUhyI_d0vfnxyYznq3I4,20395
docx/oxml/settings.py,sha256=eayU_BPA6_SGvIE-u7lnWuuK5esSVrBi6X5UnqaHlHs,4264
docx/oxml/shape.py,sha256=e1JDsDdTZMgopMQz4j5GqkSJg26vfXzM9NXT1OpM3Vw,9012
docx/oxml/shared.py,sha256=jlKHUrRhquiWeXf9DUne3BOrqcKdePOhFGtRrnuLLhk,1875
docx/oxml/simpletypes.py,sha256=Y4imeqR1--qDWwIYP5lMgmhG-qQ8a0hxTU12rTDXayQ,10834
docx/oxml/styles.py,sha256=1U24kcC9hhzMPzNcg19RVCUJl1_qWauXCJIHG_Kp0Io,10643
docx/oxml/table.py,sha256=54oSO8bdIY7iYqFWJ5fWt9Pi1QmXzwXP-DiYyvMQe8s,33632
docx/oxml/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/oxml/text/__pycache__/__init__.cpython-313.pyc,,
docx/oxml/text/__pycache__/font.cpython-313.pyc,,
docx/oxml/text/__pycache__/hyperlink.cpython-313.pyc,,
docx/oxml/text/__pycache__/pagebreak.cpython-313.pyc,,
docx/oxml/text/__pycache__/paragraph.cpython-313.pyc,,
docx/oxml/text/__pycache__/parfmt.cpython-313.pyc,,
docx/oxml/text/__pycache__/run.cpython-313.pyc,,
docx/oxml/text/font.py,sha256=UZB_GnLSFPj0xikWreQYR4uSaI27XbsjTEdWCqmzbvQ,11499
docx/oxml/text/hyperlink.py,sha256=tx6SQiYaa123-a0JfUS80mtACOYwNoy24Trga2Shgn8,1467
docx/oxml/text/pagebreak.py,sha256=qCdFUTM2X96TnRyrlYW6k7gi_vzQmM6wdXNJ9ZoW4w4,11262
docx/oxml/text/paragraph.py,sha256=op4Cy4LjcSIxDD1CQIKeRbUsBL5ABVR8cYKb3KynlmQ,3538
docx/oxml/text/parfmt.py,sha256=IT2VsjZyUn832p-3_9jO0ZJT90jDr7MzcrFL6ZrL_jk,11986
docx/oxml/text/run.py,sha256=w6MPbSbuHd3lqYvgh32log3mAmu4WU10IOsIqMPtNPY,9629
docx/oxml/xmlchemy.py,sha256=jgAmF6RFO0ohahfDN7ACh7DfhRt0rZwS6S1-WrXwkEg,25567
docx/package.py,sha256=btnIJEMJBmA7zymO43vbNqDDFm1ZoEiB4nN2sgn6sug,3971
docx/parts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/parts/__pycache__/__init__.cpython-313.pyc,,
docx/parts/__pycache__/document.cpython-313.pyc,,
docx/parts/__pycache__/hdrftr.cpython-313.pyc,,
docx/parts/__pycache__/image.cpython-313.pyc,,
docx/parts/__pycache__/numbering.cpython-313.pyc,,
docx/parts/__pycache__/settings.cpython-313.pyc,,
docx/parts/__pycache__/story.cpython-313.pyc,,
docx/parts/__pycache__/styles.cpython-313.pyc,,
docx/parts/document.py,sha256=wFZs3hIwqzv-AD-vdSTI5ei6BB6WplQICinvdwCAG6Q,5580
docx/parts/hdrftr.py,sha256=_U0PCjVkK6EWwkyZb9oTunlNb6x55rECD1xFDzwUlPM,1709
docx/parts/image.py,sha256=c-DF5YFvp1qVTx7Zon-xOXw0qPc_3LoeX9qMSFPs6mo,2739
docx/parts/numbering.py,sha256=Hh0nghBw1IWm1Z1S-EVOv2Lm4rJ6y3-kQ8l65BQSqJE,1056
docx/parts/settings.py,sha256=cJ8sO7wzLB9tGu2LAq572-Uu_8MGxfRf0r9DWSptm5Y,1725
docx/parts/story.py,sha256=_hrq7Z-uIZtwFUrK3PKMmGhOcu_Yguej1xNr-qbJssI,3857
docx/parts/styles.py,sha256=-kETvhAZOwCGjtJDdOFEjvjNrdKCoTaVGoGeyxXZmsc,1413
docx/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/section.py,sha256=r2SLP6EXCtHPmv8PtaTuYZg1Wy8l9MoVHlA9JHT3SiA,18412
docx/settings.py,sha256=tWJUOj3KABgk_3bXoxhIHVWVY5HmmKKl2tsqic2YN_c,1082
docx/shape.py,sha256=5agasreXHsVIOdd1z8d-3erPvlsbBRJQjHQ0ILSfeaI,2969
docx/shared.py,sha256=_ss87ReX0QWCdKw9I4LhJA_pWbufDY06HbBbONes_l8,13509
docx/styles/__init__.py,sha256=aCJqhH4JhG_cIm3iwVJGtWpWW0AXJx3QMag7MORmrdI,1406
docx/styles/__pycache__/__init__.cpython-313.pyc,,
docx/styles/__pycache__/latent.cpython-313.pyc,,
docx/styles/__pycache__/style.cpython-313.pyc,,
docx/styles/__pycache__/styles.cpython-313.pyc,,
docx/styles/latent.py,sha256=EDBcgd-1jKmW-wGz64GvcGSopnOuPQG1inU0pnHRsQA,7211
docx/styles/style.py,sha256=BoGXQRSyODmPI38bAEWPgTXEPwhXyVYZZ9nSYxRSHPM,8051
docx/styles/styles.py,sha256=ib2u983b5aFzfxiSM6w0cpMSPWul6kgSVEVCA2q5vJg,5717
docx/table.py,sha256=EFxbVD3aEbLOqdes5cpGt3xXbPzbZLFG0Sl5OPHSrhY,19477
docx/templates/default-docx-template/[Content_Types].xml,sha256=iIp82dldCZhDakmXWfMFMDUM4az4TTwH70ftarpInp4,1782
docx/templates/default-docx-template/_rels/.rels,sha256=-Lwgt-HVkmKrvcSPn_pqyldGhoi71A3GJ4F3MnMlJrM,748
docx/templates/default-docx-template/customXml/_rels/item1.xml.rels,sha256=Mvpt31vf_e7JfnfQgFko9NJBHyOjM10e4ESbdAzh2E0,300
docx/templates/default-docx-template/customXml/item1.xml,sha256=qGCG_8XY6D69bHGlXR0u-qMbE3l39fOnUjZuECNhIUQ,262
docx/templates/default-docx-template/customXml/itemProps1.xml,sha256=xUIwexPsKai1RiF7s3k2q0gi4ESyZdKVKYXsPWr-0k4,354
docx/templates/default-docx-template/docProps/app.xml,sha256=vmZJgcMUHN38WTYr6yh-vyDQdzZg4t1vqsWWilkwoIE,1132
docx/templates/default-docx-template/docProps/core.xml,sha256=EL_SDqXZyKsCNqL05J-ZzbIHqsZxHkeAz3o5CzIqHUA,753
docx/templates/default-docx-template/docProps/thumbnail.jpeg,sha256=ljZxONxEzgm_LI8Pjkk0ihR40sXAr2m7wrvDi2PNzq0,8324
docx/templates/default-docx-template/word/_rels/document.xml.rels,sha256=Tyt7R9in0Q1E47rW0Dgpui51XMiymi01pJ7vee17EdY,1253
docx/templates/default-docx-template/word/document.xml,sha256=9RVLPF2udJq7DjgeEOoC4Z4TdlAGXEsfEbBZxtdTQL8,1594
docx/templates/default-docx-template/word/fontTable.xml,sha256=eThft_YCR1B-yv_Ckunr1S6gZXuGNPYpum_MxUAR1rs,2811
docx/templates/default-docx-template/word/numbering.xml,sha256=ymBdZOm6Iy_Y_0AdcAZp97XENRhlscFrkf1aSqyb4kk,6747
docx/templates/default-docx-template/word/settings.xml,sha256=MVItLTw2auxXFMkhzlVrSDjKf6oz-nlaZtwyV1xxCyc,2749
docx/templates/default-docx-template/word/styles.xml,sha256=CeNQuV4SHntjhBSFpq2s8vrNSWwYkpey26Y0u7KJiog,438677
docx/templates/default-docx-template/word/stylesWithEffects.xml,sha256=Rjrgkozw2Ed12_jPGNbDAp9nB8gb9ZD21t2HV6XpPxU,438131
docx/templates/default-docx-template/word/theme/theme1.xml,sha256=46irfbnKevylb18oIKVui2YAFsZHdzVVsGCwoCrHaUE,10939
docx/templates/default-docx-template/word/webSettings.xml,sha256=NJ023nQ00J-GmH_2cdiBSWSgWIweYwwG5WLNp-den5U,438
docx/templates/default-footer.xml,sha256=V9Gkf2J3fxHVNphYHX2FfxuSdHUh1_tArHv1ZlUHAxU,1395
docx/templates/default-header.xml,sha256=oMHJ5mr6Sd1PZkmEQEJ4lmr7VCwZiFOAvmJSsOKK4UI,1395
docx/templates/default-settings.xml,sha256=eXOLlF4o4eqDOLeEi5qjwTv5mmDk3V5CMseQD7_bCug,1640
docx/templates/default-styles.xml,sha256=WnjtOZLsCnP4hYSaYhMfR7JW4ixDxRcr3eBPI8QRx94,15823
docx/templates/default.docx,sha256=IJS1vd_-nPlz1h_gM4hBOATwNBYHGElKZdt-mNpA010,38116
docx/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/text/__pycache__/__init__.cpython-313.pyc,,
docx/text/__pycache__/font.cpython-313.pyc,,
docx/text/__pycache__/hyperlink.cpython-313.pyc,,
docx/text/__pycache__/pagebreak.cpython-313.pyc,,
docx/text/__pycache__/paragraph.cpython-313.pyc,,
docx/text/__pycache__/parfmt.cpython-313.pyc,,
docx/text/__pycache__/run.cpython-313.pyc,,
docx/text/__pycache__/tabstops.cpython-313.pyc,,
docx/text/font.py,sha256=oG2viOmsz_8AK-ZKqKvNSt06ICUFSSAH0eDrJN2PXF8,13653
docx/text/hyperlink.py,sha256=H7YGCH85i7EDcPQcYoLh4MOx_w-xNSucA5bAgI3HtVQ,5257
docx/text/pagebreak.py,sha256=sI1yMVotkBtn1u5DJ9L04FkC01QoAwcyVSdgGPAkJIY,4950
docx/text/paragraph.py,sha256=N3F62_JRQZYaumuoaJXKCDnRSw2DIiOwzyMPV2Jd7GI,6828
docx/text/parfmt.py,sha256=70M75HFo7ydpqmIdZjkV711muyrsBGdr4WZscF3YVJY,10339
docx/text/run.py,sha256=cMA62dEnO-raUkTZ2iuUyXhkNhRVpZNvMYDo5h40zgM,9657
docx/text/tabstops.py,sha256=JM4zkno4jyYq5aWIXVz3axFOwB94_H6KJc02CAzuCXQ,3910
docx/types.py,sha256=V-xkBXIXkv5LZrfLlAPL0Ebzt-_gklmHaY461nGQEFk,864
python_docx-1.1.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
python_docx-1.1.2.dist-info/LICENSE,sha256=dlLyceRtDVM-ncRj87X8vcrPTWqcjWtVTRXv0PN_YTI,1104
python_docx-1.1.2.dist-info/METADATA,sha256=IYd34KO2W_SK_8hyD-_HvOxCEUuaWnt12F5MNq--294,2002
python_docx-1.1.2.dist-info/RECORD,,
python_docx-1.1.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
python_docx-1.1.2.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
python_docx-1.1.2.dist-info/top_level.txt,sha256=zkbrD3gmyqHpD_XhHe7lCbr2M5vT229nJ4UFqF5VjIs,5
