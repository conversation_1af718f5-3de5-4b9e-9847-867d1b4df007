# تعليمات نسخة الاختبار

## الملف: test_nahda_sample.sql

### المحتوى:
- 30 تصنيف هرمي
- 100 مقال من الكتاب
- مثالي للاختبار السريع

### خطوات الاستيراد:

#### 1. عبر phpMyAdmin:
- ادخل إلى phpMyAdmin
- اختر قاعدة البيانات
- اذهب إلى تبويب "استيراد"
- اختر الملف test_nahda_sample.sql
- اضغط "تنفيذ"

#### 2. عبر سطر الأوامر:
```bash
mysql -u username -p database_name < test_nahda_sample.sql
```

#### 3. عبر WordPress CLI:
```bash
wp db import test_nahda_sample.sql
```

### الوقت المتوقع: 1-3 دقائق

### بعد الاستيراد:
1. ادخل إلى لوحة تحكم WordPress
2. اذهب إلى "المقالات" → "التصنيفات"
3. تحقق من وجود التصنيفات الهرمية
4. اذهب إلى "المقالات" → "جميع المقالات"
5. تحقق من وجود المقالات مع التصنيفات

### استكشاف الأخطاء:
- تأكد من أن قاعدة البيانات فارغة أو انشئ نسخة احتياطية
- تأكد من إعدادات UTF-8 في قاعدة البيانات
- تحقق من صلاحيات المستخدم

### إذا نجح الاختبار:
يمكنك الانتقال إلى استيراد الملفات الكاملة:
- 01_categories.sql (جميع التصنيفات)
- 02_posts.sql إلى 11_posts.sql (جميع المقالات)
- 99_setup.sql (الإعدادات النهائية)
