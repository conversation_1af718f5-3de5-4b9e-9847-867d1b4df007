#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أسماء التصنيفات الطويلة
حل مشكلة فشل استيراد التصنيفات بأسماء طويلة
"""

import json
import os
import re
from datetime import datetime

class CategoryNameFixer:
    """مُصحح أسماء التصنيفات"""
    
    def __init__(self):
        self.wp_structure = None
        self.fixed_categories = []
        
    def load_structure(self):
        """تحميل بنية WordPress"""
        try:
            with open('wordpress_structure_ready.json', 'r', encoding='utf-8') as f:
                self.wp_structure = json.load(f)
            
            print(f"✅ تم تحميل البنية:")
            print(f"   📂 التصنيفات: {len(self.wp_structure['categories'])}")
            return True
            
        except FileNotFoundError:
            print("❌ لم يتم العثور على ملف البنية")
            return False
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False
    
    def fix_category_name(self, name, max_length=80):
        """إصلاح اسم التصنيف الطويل"""
        if len(name) <= max_length:
            return name
        
        # قواعد الاختصار الذكي
        
        # 1. إزالة النصوص التوضيحية الطويلة
        patterns_to_remove = [
            r':\s*.*$',  # إزالة كل شيء بعد النقطتين
            r'،\s*.*$',  # إزالة كل شيء بعد الفاصلة الأولى
            r'\.\s*.*$', # إزالة كل شيء بعد النقطة الأولى
        ]
        
        for pattern in patterns_to_remove:
            shortened = re.sub(pattern, '', name).strip()
            if len(shortened) <= max_length and len(shortened) > 10:
                return shortened
        
        # 2. اختصار الكلمات الشائعة
        replacements = {
            'مشروع النهضة': 'النهضة',
            'وبناء الدولة السورية': '',
            'ما بعد الاستبداد': '',
            'الاستجابة الإسعافية': 'الإسعافية',
            'البناء المؤسسي': 'البناء',
            'الاستدامة والنمو': 'الاستدامة',
            'التمكين والسيادة': 'التمكين',
            'البناء السريع': 'البناء',
            'التوسع والتأصيل': 'التوسع',
        }
        
        shortened = name
        for old, new in replacements.items():
            shortened = shortened.replace(old, new)
        
        shortened = re.sub(r'\s+', ' ', shortened).strip()
        
        if len(shortened) <= max_length:
            return shortened
        
        # 3. اقتطاع ذكي
        words = shortened.split()
        result = []
        current_length = 0
        
        for word in words:
            if current_length + len(word) + 1 <= max_length - 3:  # -3 للنقاط
                result.append(word)
                current_length += len(word) + 1
            else:
                break
        
        if result:
            return ' '.join(result) + '...'
        else:
            # آخر حل: اقتطاع مباشر
            return name[:max_length-3] + '...'
    
    def create_smart_slug(self, name, max_length=50):
        """إنشاء slug ذكي"""
        # تنظيف الاسم
        slug = name.replace('...', '')
        
        # إزالة الأحرف الخاصة
        slug = re.sub(r'[^\w\s-]', '', slug)
        
        # استبدال المسافات بشرطات
        slug = re.sub(r'[-\s]+', '-', slug)
        
        # تنظيف الشرطات في البداية والنهاية
        slug = slug.strip('-')
        
        # اقتطاع إذا كان طويلاً
        if len(slug) > max_length:
            slug = slug[:max_length].rstrip('-')
        
        return slug.lower()
    
    def fix_all_categories(self):
        """إصلاح جميع التصنيفات"""
        print("🔧 إصلاح أسماء التصنيفات الطويلة...")
        
        fixed_count = 0
        
        for category in self.wp_structure['categories']:
            original_name = category['name']
            
            # إصلاح الاسم إذا كان طويلاً
            if len(original_name) > 80:
                fixed_name = self.fix_category_name(original_name)
                fixed_slug = self.create_smart_slug(fixed_name)
                
                print(f"🔧 إصلاح:")
                print(f"   ❌ الأصلي: {original_name[:60]}...")
                print(f"   ✅ المُصحح: {fixed_name}")
                print(f"   🔗 Slug: {fixed_slug}")
                print()
                
                category['name'] = fixed_name
                category['slug'] = fixed_slug
                category['original_name'] = original_name
                
                fixed_count += 1
            else:
                # تحسين slug للأسماء العادية
                category['slug'] = self.create_smart_slug(original_name)
        
        print(f"✅ تم إصلاح {fixed_count} تصنيف")
        return fixed_count > 0
    
    def save_fixed_structure(self):
        """حفظ البنية المُصححة"""
        with open('wordpress_structure_fixed.json', 'w', encoding='utf-8') as f:
            json.dump(self.wp_structure, f, ensure_ascii=False, indent=2)
        
        print("✅ تم حفظ البنية المُصححة في: wordpress_structure_fixed.json")
    
    def create_xml_header(self):
        """إنشاء رأس ملف XML"""
        return '''<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0"
    xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:wfw="http://wellformedweb.org/CommentAPI/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:wp="http://wordpress.org/export/1.2/">

<channel>
    <title>مشروع النهضة السورية</title>
    <link>http://localhost</link>
    <description>كتاب مشروع النهضة وبناء الدولة السورية</description>
    <pubDate>{}</pubDate>
    <language>ar</language>
    <wp:wxr_version>1.2</wp:wxr_version>
    <wp:base_site_url>http://localhost</wp:base_site_url>
    <wp:base_blog_url>http://localhost</wp:base_blog_url>

    <wp:author>
        <wp:author_id>1</wp:author_id>
        <wp:author_login>admin</wp:author_login>
        <wp:author_email><EMAIL></wp:author_email>
        <wp:author_display_name><![CDATA[مؤلف النهضة]]></wp:author_display_name>
        <wp:author_first_name><![CDATA[]]></wp:author_first_name>
        <wp:author_last_name><![CDATA[]]></wp:author_last_name>
    </wp:author>
'''.format(datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000'))
    
    def create_xml_footer(self):
        """إنشاء تذييل ملف XML"""
        return '''
</channel>
</rss>'''
    
    def escape_xml(self, text):
        """تنظيف النص لـ XML"""
        if not text:
            return ''
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#39;'))
    
    def create_category_xml(self, category):
        """إنشاء XML للتصنيف المُصحح"""
        cat_id = category['id']
        name = self.escape_xml(category['name'])
        slug = category['slug']
        
        # تحديد الأب
        parent_slug = ''
        if category['parent'] > 0:
            parent_cat = next((cat for cat in self.wp_structure['categories'] 
                             if cat['id'] == category['parent']), None)
            if parent_cat:
                parent_slug = parent_cat['slug']
        
        # وصف محسن
        descriptions = {
            'part': 'قسم رئيسي من مشروع النهضة',
            'chapter': 'باب من أبواب المشروع',
            'section': 'فصل تفصيلي',
            'subsection': 'مبحث أو محور فرعي'
        }
        description = descriptions.get(category.get('type', ''), 'تصنيف من كتاب النهضة')
        
        return f'''
    <wp:category>
        <wp:term_id>{cat_id}</wp:term_id>
        <wp:category_nicename>{slug}</wp:category_nicename>
        <wp:category_parent>{parent_slug}</wp:category_parent>
        <wp:cat_name><![CDATA[{name}]]></wp:cat_name>
        <wp:category_description><![CDATA[{description}]]></wp:category_description>
    </wp:category>'''
    
    def create_fixed_categories_file(self):
        """إنشاء ملف التصنيفات المُصحح"""
        print("📂 إنشاء ملف التصنيفات المُصحح...")
        
        content = self.create_xml_header()
        
        # ترتيب التصنيفات حسب المستوى
        sorted_categories = sorted(self.wp_structure['categories'], 
                                 key=lambda x: (x.get('level', 1), x.get('id', 0)))
        
        for category in sorted_categories:
            content += self.create_category_xml(category)
        
        content += self.create_xml_footer()
        
        filename = 'hierarchical_categories_fixed.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({size:.1f} KB - {len(sorted_categories)} تصنيف)")
        return filename
    
    def create_comparison_report(self):
        """إنشاء تقرير مقارنة"""
        report = f"""# 📊 تقرير إصلاح أسماء التصنيفات

## 🎯 الهدف
إصلاح مشكلة فشل استيراد التصنيفات بأسماء طويلة في WordPress.

## 📋 المشاكل المُصححة

### ❌ **المشاكل الأصلية:**
- أسماء تصنيفات أطول من 80 حرف
- slugs طويلة وغير متوافقة
- فشل في الاستيراد

### ✅ **الحلول المُطبقة:**
- اختصار الأسماء الطويلة بذكاء
- إنشاء slugs محسنة
- الحفاظ على المعنى الأصلي

## 📊 الإحصائيات

| العنصر | العدد |
|---------|-------|
| إجمالي التصنيفات | {len(self.wp_structure['categories'])} |
| التصنيفات المُصححة | {len([cat for cat in self.wp_structure['categories'] if 'original_name' in cat])} |
| التصنيفات السليمة | {len([cat for cat in self.wp_structure['categories'] if 'original_name' not in cat])} |

## 🔧 أمثلة على الإصلاحات

"""
        
        # إضافة أمثلة على الإصلاحات
        fixed_examples = [cat for cat in self.wp_structure['categories'] if 'original_name' in cat][:5]
        
        for i, cat in enumerate(fixed_examples, 1):
            report += f"""
### {i}. **مثال {i}:**
- **الأصلي:** {cat['original_name'][:80]}...
- **المُصحح:** {cat['name']}
- **Slug:** {cat['slug']}
"""
        
        report += f"""

## 🚀 الخطوات التالية

### 1️⃣ **استخدم الملف المُصحح:**
```
أدوات → استيراد → WordPress → hierarchical_categories_fixed.xml
```

### 2️⃣ **تحقق من النتائج:**
- يجب أن تستورد جميع التصنيفات بنجاح
- تحقق من البنية الهرمية
- تأكد من صحة الروابط

## ✅ النتيجة المتوقعة

**جميع التصنيفات ستستورد بنجاح دون أخطاء!**

---

**📅 تاريخ الإنشاء:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open('CATEGORY_FIX_REPORT.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✅ تم إنشاء: CATEGORY_FIX_REPORT.md")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح أسماء التصنيفات الطويلة")
    print("=" * 50)
    
    fixer = CategoryNameFixer()
    
    # تحميل البنية
    if not fixer.load_structure():
        return False
    
    # إصلاح التصنيفات
    if fixer.fix_all_categories():
        # حفظ البنية المُصححة
        fixer.save_fixed_structure()
        
        # إنشاء ملف XML مُصحح
        fixer.create_fixed_categories_file()
        
        # إنشاء تقرير المقارنة
        fixer.create_comparison_report()
        
        print(f"\n" + "=" * 50)
        print("🎉 تم إصلاح المشكلة بنجاح!")
        print(f"\n🎯 الخطوات التالية:")
        print(f"   1. استخدم hierarchical_categories_fixed.xml بدلاً من الملف الأصلي")
        print(f"   2. اقرأ CATEGORY_FIX_REPORT.md للتفاصيل")
        print(f"   3. استورد الملف الجديد في WordPress")
        
        return True
    else:
        print("ℹ️ لا توجد تصنيفات تحتاج إصلاح")
        return True

if __name__ == "__main__":
    main()
