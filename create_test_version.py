#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Test Version SQL
Quick test version with limited content
"""

import json
from accurate_sql_generator import AccurateSQLGenerator

def create_test_sql():
    """Create test version with limited content"""
    print("🧪 إنشاء نسخة اختبار محدودة...")
    
    # Load structure
    try:
        with open('wordpress_structure.json', 'r', encoding='utf-8') as f:
            structure = json.load(f)
    except:
        print("❌ لم يتم العثور على ملف البنية")
        return False
    
    # Create limited structure
    limited_structure = {
        'categories': structure['categories'][:30],  # First 30 categories
        'posts': structure['posts'][:100]  # First 100 posts
    }
    
    print(f"📊 نسخة الاختبار:")
    print(f"   📂 التصنيفات: {len(limited_structure['categories'])}")
    print(f"   📄 المقالات: {len(limited_structure['posts'])}")
    
    # Generate SQL
    generator = AccurateSQLGenerator()
    generator.wp_structure = limited_structure
    
    if generator.generate_complete_sql():
        if generator.save_sql_file('test_nahda_sample.sql'):
            create_test_instructions()
            print("\n✅ تم إنشاء نسخة الاختبار بنجاح!")
            print("📁 الملف: test_nahda_sample.sql")
            print("⏱️ وقت الاستيراد المتوقع: 1-3 دقائق")
            return True
    
    return False

def create_test_instructions():
    """Create instructions for test version"""
    instructions = """# تعليمات نسخة الاختبار

## الملف: test_nahda_sample.sql

### المحتوى:
- 30 تصنيف هرمي
- 100 مقال من الكتاب
- مثالي للاختبار السريع

### خطوات الاستيراد:

#### 1. عبر phpMyAdmin:
- ادخل إلى phpMyAdmin
- اختر قاعدة البيانات
- اذهب إلى تبويب "استيراد"
- اختر الملف test_nahda_sample.sql
- اضغط "تنفيذ"

#### 2. عبر سطر الأوامر:
```bash
mysql -u username -p database_name < test_nahda_sample.sql
```

#### 3. عبر WordPress CLI:
```bash
wp db import test_nahda_sample.sql
```

### الوقت المتوقع: 1-3 دقائق

### بعد الاستيراد:
1. ادخل إلى لوحة تحكم WordPress
2. اذهب إلى "المقالات" → "التصنيفات"
3. تحقق من وجود التصنيفات الهرمية
4. اذهب إلى "المقالات" → "جميع المقالات"
5. تحقق من وجود المقالات مع التصنيفات

### استكشاف الأخطاء:
- تأكد من أن قاعدة البيانات فارغة أو انشئ نسخة احتياطية
- تأكد من إعدادات UTF-8 في قاعدة البيانات
- تحقق من صلاحيات المستخدم

### إذا نجح الاختبار:
يمكنك الانتقال إلى استيراد الملفات الكاملة:
- 01_categories.sql (جميع التصنيفات)
- 02_posts.sql إلى 11_posts.sql (جميع المقالات)
- 99_setup.sql (الإعدادات النهائية)
"""
    
    with open('test_instructions.md', 'w', encoding='utf-8') as f:
        f.write(instructions)

if __name__ == "__main__":
    create_test_sql()
