#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WordPress XML Validator
Validates WXR format and checks for common issues
"""

import xml.etree.ElementTree as ET
import os
import glob

def validate_wxr_file(filename):
    """Validate a single WXR file"""
    print(f"\n🔍 فحص ملف: {filename}")
    
    if not os.path.exists(filename):
        print(f"❌ الملف غير موجود: {filename}")
        return False
    
    try:
        # Parse XML
        tree = ET.parse(filename)
        root = tree.getroot()
        
        # Check root element
        if root.tag != 'rss':
            print(f"❌ العنصر الجذر خاطئ: {root.tag} (يجب أن يكون 'rss')")
            return False
        
        # Check RSS version
        version = root.get('version')
        if version != '2.0':
            print(f"⚠️ إصدار RSS: {version} (يُفضل 2.0)")
        
        # Check namespaces
        required_namespaces = [
            'http://wordpress.org/export/1.2/',
            'http://purl.org/rss/1.0/modules/content/',
            'http://purl.org/dc/elements/1.1/'
        ]
        
        namespaces_found = []
        for key, value in root.attrib.items():
            if key.startswith('xmlns'):
                namespaces_found.append(value)
        
        missing_namespaces = []
        for ns in required_namespaces:
            if ns not in namespaces_found:
                missing_namespaces.append(ns)
        
        if missing_namespaces:
            print(f"⚠️ مساحات أسماء مفقودة: {missing_namespaces}")
        
        # Find channel
        channel = root.find('channel')
        if channel is None:
            print("❌ عنصر 'channel' مفقود")
            return False
        
        # Check WXR version
        wxr_version = channel.find('.//{http://wordpress.org/export/1.2/}wxr_version')
        if wxr_version is None:
            print("❌ عنصر 'wxr_version' مفقود - هذا سبب الخطأ الرئيسي!")
            return False
        else:
            print(f"✅ WXR Version: {wxr_version.text}")
        
        # Check required elements
        required_elements = ['title', 'link', 'description']
        for elem in required_elements:
            if channel.find(elem) is None:
                print(f"⚠️ عنصر مفقود: {elem}")
        
        # Count categories and posts
        categories = channel.findall('.//{http://wordpress.org/export/1.2/}category')
        items = channel.findall('item')
        
        print(f"📁 التصنيفات: {len(categories)}")
        print(f"📄 المقالات: {len(items)}")
        
        # Check file size
        file_size = os.path.getsize(filename) / (1024 * 1024)  # MB
        print(f"📊 حجم الملف: {file_size:.2f} MB")
        
        if file_size > 8:
            print("⚠️ الملف كبير - قد تحتاج لزيادة حدود الرفع")
        
        # Check Arabic content
        arabic_found = False
        for item in items[:5]:  # Check first 5 items
            title = item.find('title')
            if title is not None and title.text:
                # Check for Arabic characters
                if any('\u0600' <= char <= '\u06FF' for char in title.text):
                    arabic_found = True
                    break
        
        if arabic_found:
            print("✅ تم العثور على نص عربي")
        else:
            print("⚠️ لم يتم العثور على نص عربي")
        
        print(f"✅ الملف صالح للاستيراد: {filename}")
        return True
        
    except ET.ParseError as e:
        print(f"❌ خطأ في تحليل XML: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def validate_all_files():
    """Validate all WordPress XML files"""
    print("🔍 فحص جميع ملفات WordPress XML")
    print("=" * 50)
    
    # Find all WordPress XML files
    xml_files = []
    xml_files.extend(glob.glob('wordpress_*.xml'))
    
    if not xml_files:
        print("❌ لم يتم العثور على ملفات WordPress XML")
        return
    
    valid_files = []
    invalid_files = []
    
    for filename in sorted(xml_files):
        if validate_wxr_file(filename):
            valid_files.append(filename)
        else:
            invalid_files.append(filename)
    
    print(f"\n" + "=" * 50)
    print(f"📊 ملخص الفحص:")
    print(f"✅ ملفات صالحة: {len(valid_files)}")
    print(f"❌ ملفات غير صالحة: {len(invalid_files)}")
    
    if valid_files:
        print(f"\n✅ الملفات الصالحة للاستيراد:")
        for filename in valid_files:
            file_size = os.path.getsize(filename) / (1024 * 1024)
            print(f"   - {filename} ({file_size:.2f} MB)")
    
    if invalid_files:
        print(f"\n❌ الملفات التي تحتاج إصلاح:")
        for filename in invalid_files:
            print(f"   - {filename}")
    
    print(f"\n📋 ترتيب الاستيراد المُوصى به:")
    print(f"1. wordpress_categories.xml (التصنيفات أولاً)")
    for i, filename in enumerate(sorted([f for f in valid_files if 'posts' in f]), 2):
        print(f"{i}. {filename}")

def check_upload_requirements():
    """Check WordPress upload requirements"""
    print(f"\n🔧 متطلبات الرفع في WordPress:")
    print(f"=" * 30)
    
    xml_files = glob.glob('wordpress_*.xml')
    if xml_files:
        max_size = max(os.path.getsize(f) for f in xml_files) / (1024 * 1024)
        print(f"📊 أكبر ملف: {max_size:.2f} MB")
        
        if max_size > 2:
            print(f"⚠️ تحتاج لزيادة upload_max_filesize إلى {int(max_size) + 1}M على الأقل")
        else:
            print(f"✅ الملفات صغيرة بما فيه الكفاية للرفع العادي")
        
        print(f"\n📋 إعدادات PHP المُوصى بها:")
        print(f"upload_max_filesize = {max(8, int(max_size) + 2)}M")
        print(f"post_max_size = {max(8, int(max_size) + 2)}M")
        print(f"max_execution_time = 300")
        print(f"memory_limit = 256M")

if __name__ == "__main__":
    validate_all_files()
    check_upload_requirements()
