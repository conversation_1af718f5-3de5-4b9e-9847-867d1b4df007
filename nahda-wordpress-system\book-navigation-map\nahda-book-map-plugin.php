<?php
/**
 * Plugin Name: خارطة كتاب النهضة
 * Plugin URI: https://nahda-project.org
 * Description: خارطة تفاعلية لكتاب مشروع النهضة السورية مع البنية الهرمية الكاملة (قسم → باب → فصل → مبحث → مقال)
 * Version: 1.0.0
 * Author: مطور مشروع النهضة
 * Author URI: https://nahda-project.org
 * Text Domain: nahda-book-map
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تعريف الثوابت
define('NAHDA_BOOK_MAP_VERSION', '1.0.0');
define('NAHDA_BOOK_MAP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('NAHDA_BOOK_MAP_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * الكلاس الرئيسي لخارطة كتاب النهضة
 */
class NahdaBookMapPlugin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // تسجيل Shortcodes
        add_shortcode('nahda_book_map', array($this, 'display_book_map'));
        add_shortcode('nahda_category_tree', array($this, 'display_category_tree'));
        add_shortcode('nahda_breadcrumb', array($this, 'display_breadcrumb'));
        add_shortcode('nahda_book_stats', array($this, 'display_book_stats'));
        
        // تفعيل عند التثبيت
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        load_plugin_textdomain('nahda-book-map', false, dirname(plugin_basename(__FILE__)) . '/languages');
        $this->create_book_map_page();
        $this->add_menu_support();
    }
    
    public function enqueue_scripts() {
        wp_enqueue_script('jquery');
        
        wp_enqueue_style(
            'nahda-book-map-css',
            NAHDA_BOOK_MAP_PLUGIN_URL . 'assets/nahda-book-map.css',
            array(),
            NAHDA_BOOK_MAP_VERSION
        );
        
        wp_enqueue_script(
            'nahda-book-map-js',
            NAHDA_BOOK_MAP_PLUGIN_URL . 'assets/nahda-book-map.js',
            array('jquery'),
            NAHDA_BOOK_MAP_VERSION,
            true
        );
        
        wp_localize_script('nahda-book-map-js', 'nahdaBookMap', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('nahda_book_map_nonce'),
            'strings' => array(
                'searching' => __('جاري البحث...', 'nahda-book-map'),
                'noResults' => __('لم يتم العثور على نتائج', 'nahda-book-map'),
                'expandAll' => __('توسيع الكل', 'nahda-book-map'),
                'collapseAll' => __('طي الكل', 'nahda-book-map')
            )
        ));
    }
    
    public function add_admin_menu() {
        add_options_page(
            __('خارطة كتاب النهضة', 'nahda-book-map'),
            __('خارطة الكتاب', 'nahda-book-map'),
            'manage_options',
            'nahda-book-map',
            array($this, 'admin_page')
        );
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('خارطة كتاب النهضة', 'nahda-book-map'); ?></h1>
            
            <div class="card">
                <h2><?php _e('الاستخدام', 'nahda-book-map'); ?></h2>
                <p><?php _e('استخدم الـ Shortcodes التالية لعرض خارطة الكتاب:', 'nahda-book-map'); ?></p>
                
                <h3><?php _e('الخارطة الكاملة', 'nahda-book-map'); ?></h3>
                <code>[nahda_book_map style="tree"]</code>
                <p><?php _e('عرض شجري تفاعلي للكتاب', 'nahda-book-map'); ?></p>
                
                <code>[nahda_book_map style="grid"]</code>
                <p><?php _e('عرض شبكي بالبطاقات', 'nahda-book-map'); ?></p>
                
                <code>[nahda_book_map style="accordion"]</code>
                <p><?php _e('عرض أكورديون مضغوط', 'nahda-book-map'); ?></p>
                
                <h3><?php _e('عناصر إضافية', 'nahda-book-map'); ?></h3>
                <code>[nahda_category_tree]</code>
                <p><?php _e('شجرة التصنيفات فقط', 'nahda-book-map'); ?></p>
                
                <code>[nahda_breadcrumb]</code>
                <p><?php _e('مسار التنقل', 'nahda-book-map'); ?></p>
                
                <code>[nahda_book_stats]</code>
                <p><?php _e('إحصائيات الكتاب', 'nahda-book-map'); ?></p>
            </div>
            
            <div class="card">
                <h2><?php _e('الإحصائيات', 'nahda-book-map'); ?></h2>
                <?php $this->display_admin_stats(); ?>
            </div>
        </div>
        <?php
    }
    
    private function display_admin_stats() {
        $categories = get_categories(array('hide_empty' => false));
        $posts_count = wp_count_posts()->publish;
        
        $levels = array();
        foreach ($categories as $cat) {
            $level = $this->get_category_level($cat);
            if (!isset($levels[$level])) {
                $levels[$level] = 0;
            }
            $levels[$level]++;
        }
        
        echo '<table class="widefat">';
        echo '<thead><tr><th>المستوى</th><th>النوع</th><th>العدد</th></tr></thead>';
        echo '<tbody>';
        
        $level_names = array(
            1 => 'الأقسام',
            2 => 'الأبواب',
            3 => 'الفصول',
            4 => 'المباحث'
        );
        
        foreach ($levels as $level => $count) {
            $name = isset($level_names[$level]) ? $level_names[$level] : "المستوى $level";
            echo "<tr><td>$level</td><td>$name</td><td>$count</td></tr>";
        }
        
        echo "<tr><td>5</td><td>المقالات</td><td>$posts_count</td></tr>";
        echo '</tbody></table>';
    }
    
    public function create_book_map_page() {
        $pages = array(
            array(
                'title' => 'خارطة كتاب النهضة',
                'slug' => 'nahda-book-map',
                'content' => '[nahda_book_map style="tree"]'
            ),
            array(
                'title' => 'فهرس الكتاب',
                'slug' => 'book-index',
                'content' => '[nahda_book_map style="grid"]'
            ),
            array(
                'title' => 'محتويات الكتاب',
                'slug' => 'book-contents',
                'content' => '[nahda_book_map style="accordion"]'
            )
        );
        
        foreach ($pages as $page_data) {
            $existing_page = get_page_by_path($page_data['slug']);
            
            if (!$existing_page) {
                $page_args = array(
                    'post_title' => $page_data['title'],
                    'post_content' => $page_data['content'],
                    'post_status' => 'publish',
                    'post_type' => 'page',
                    'post_name' => $page_data['slug'],
                    'post_author' => 1,
                );
                
                wp_insert_post($page_args);
            }
        }
    }
    
    public function add_menu_support() {
        add_action('wp_nav_menu_items', array($this, 'add_book_map_to_menu'), 10, 2);
    }
    
    public function add_book_map_to_menu($items, $args) {
        return $items;
    }
    
    public function display_book_map($atts) {
        $atts = shortcode_atts(array(
            'style' => 'tree',
            'show_counts' => 'true',
            'max_depth' => 5,
            'show_search' => 'true',
            'show_stats' => 'true'
        ), $atts);
        
        ob_start();
        
        echo '<div class="nahda-book-map">';
        echo '<h2>🗺️ خارطة كتاب مشروع النهضة السورية</h2>';
        
        if ($atts['show_stats'] === 'true') {
            $this->display_book_statistics();
        }
        
        if ($atts['show_search'] === 'true') {
            $this->display_quick_search();
        }
        
        switch ($atts['style']) {
            case 'grid':
                $this->display_grid_map($atts);
                break;
            case 'accordion':
                $this->display_accordion_map($atts);
                break;
            default:
                $this->display_tree_map($atts);
                break;
        }
        
        echo '</div>';
        
        return ob_get_clean();
    }
    
    // باقي الدوال سيتم إضافتها في ملف منفصل
    // لتوفير المساحة في هذا الملف
    
    public function activate() {
        $this->create_book_map_page();
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    private function get_category_level($category) {
        $level = 1;
        $current = $category;
        
        while ($current->parent != 0) {
            $level++;
            $current = get_category($current->parent);
        }
        
        return $level;
    }
    
    // دوال العرض المختلفة
    private function display_book_statistics() {
        // سيتم تضمينها من الملف الأصلي
    }
    
    private function display_quick_search() {
        // سيتم تضمينها من الملف الأصلي
    }
    
    private function display_tree_map($atts) {
        // سيتم تضمينها من الملف الأصلي
    }
    
    private function display_grid_map($atts) {
        // سيتم تضمينها من الملف الأصلي
    }
    
    private function display_accordion_map($atts) {
        // سيتم تضمينها من الملف الأصلي
    }
    
    public function display_category_tree($atts) {
        return '<div class="nahda-category-tree">شجرة التصنيفات</div>';
    }
    
    public function display_breadcrumb($atts) {
        return '<div class="nahda-breadcrumb">مسار التنقل</div>';
    }
    
    public function display_book_stats($atts) {
        return '<div class="nahda-book-stats">إحصائيات الكتاب</div>';
    }
}

// تشغيل Plugin
function nahda_book_map_init() {
    return NahdaBookMapPlugin::get_instance();
}

add_action('plugins_loaded', 'nahda_book_map_init');

// دوال مساعدة للاستخدام في القوالب
function nahda_display_book_map($style = 'tree') {
    echo do_shortcode('[nahda_book_map style="' . $style . '"]');
}

function nahda_display_breadcrumb() {
    echo do_shortcode('[nahda_breadcrumb]');
}

function nahda_display_book_stats() {
    echo do_shortcode('[nahda_book_stats]');
}
?>
