#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Document Structure Analysis
"""

import docx
import re

def quick_analysis():
    try:
        doc = docx.Document('Nahda.docx')
        print(f"Document loaded: {len(doc.paragraphs)} paragraphs")
        
        # Look for Arabic structural keywords
        keywords = ['الباب', 'الفصل', 'المبحث', 'المطلب', 'الفرع']
        found_structures = []
        
        # Check first 1000 paragraphs for structure
        for i in range(min(1000, len(doc.paragraphs))):
            para = doc.paragraphs[i]
            text = para.text.strip()
            
            if text and any(keyword in text for keyword in keywords):
                found_structures.append({
                    'index': i,
                    'text': text[:100],
                    'style': para.style.name if para.style else 'Normal'
                })
                
        print(f"\nFound {len(found_structures)} structural elements in first 1000 paragraphs:")
        for item in found_structures[:20]:  # Show first 20
            print(f"Para {item['index']}: {item['text']}")
            
        # Check middle section
        middle_start = len(doc.paragraphs) // 2
        middle_structures = []
        
        for i in range(middle_start, min(middle_start + 500, len(doc.paragraphs))):
            para = doc.paragraphs[i]
            text = para.text.strip()
            
            if text and any(keyword in text for keyword in keywords):
                middle_structures.append({
                    'index': i,
                    'text': text[:100]
                })
                
        print(f"\nFound {len(middle_structures)} structural elements in middle section:")
        for item in middle_structures[:10]:  # Show first 10
            print(f"Para {item['index']}: {item['text']}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    quick_analysis()
