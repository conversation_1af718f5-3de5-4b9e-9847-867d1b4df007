#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Accurate SQL Generator for WordPress
Based on real book structure analysis
"""

import json
import re
from datetime import datetime

class AccurateSQLGenerator:
    """Generate accurate SQL based on real book structure"""

    def __init__(self, table_prefix='wp_'):
        self.table_prefix = table_prefix
        self.sql_statements = []
        self.category_id_counter = 100  # Start from 100 to avoid conflicts
        self.post_id_counter = 1
        self.term_taxonomy_id_counter = 100
        self.wp_structure = None

    def load_structure(self):
        """Load the analyzed WordPress structure"""
        try:
            with open('wordpress_structure.json', 'r', encoding='utf-8') as f:
                self.wp_structure = json.load(f)
            print(f"✅ تم تحميل البنية المحللة")
            print(f"   📂 التصنيفات: {len(self.wp_structure['categories'])}")
            print(f"   📄 المقالات: {len(self.wp_structure['posts'])}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False

    def escape_sql(self, text):
        """Escape text for SQL"""
        if not text:
            return ''
        return text.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')

    def generate_slug(self, text):
        """Generate URL-friendly slug"""
        if not text:
            return f"item-{self.category_id_counter}"

        # Clean Arabic text and create slug
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        slug = slug.strip('-').lower()

        # If empty after cleaning, create generic slug
        if not slug:
            slug = f"category-{self.category_id_counter}"

        return slug[:50]

    def add_sql_comment(self, comment):
        """Add SQL comment"""
        self.sql_statements.append(f"\n-- {comment}")
        self.sql_statements.append("-- " + "="*50)

    def generate_categories_sql(self):
        """Generate SQL for all categories"""
        self.add_sql_comment("إنشاء التصنيفات الهرمية")

        # Create mapping for parent relationships
        category_mapping = {}

        # First pass: Create all categories
        for category in self.wp_structure['categories']:
            term_id = self.category_id_counter
            name = self.escape_sql(category['name'][:200])
            slug = self.generate_slug(category['name'])

            # Insert into wp_terms
            self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}terms (term_id, name, slug, term_group)
VALUES ({term_id}, '{name}', '{slug}', 0);""")

            # Store mapping
            category_mapping[category['id']] = {
                'term_id': term_id,
                'term_taxonomy_id': self.term_taxonomy_id_counter,
                'parent': category.get('parent', 0),
                'level': category.get('level', 1)
            }

            self.category_id_counter += 1
            self.term_taxonomy_id_counter += 1

        # Second pass: Create term_taxonomy with correct parent relationships
        for category in self.wp_structure['categories']:
            mapping = category_mapping[category['id']]

            # Find parent term_taxonomy_id
            parent_id = 0
            if mapping['parent'] and mapping['parent'] in category_mapping:
                parent_id = category_mapping[mapping['parent']]['term_taxonomy_id']

            # Insert into wp_term_taxonomy
            self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count)
VALUES ({mapping['term_taxonomy_id']}, {mapping['term_id']}, 'category', '', {parent_id}, 0);""")

        # Store mapping for posts
        self.category_mapping = category_mapping
        print(f"✅ تم إنشاء {len(self.wp_structure['categories'])} تصنيف")

    def generate_posts_sql(self):
        """Generate SQL for all posts"""
        self.add_sql_comment("إنشاء جميع المقالات")

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        for post in self.wp_structure['posts']:  # Include ALL posts
            post_id = self.post_id_counter

            # Prepare post data
            title = self.escape_sql(post['title'][:300])
            content = self.escape_sql(post['content'][:5000])  # Limit content length
            excerpt = self.escape_sql(post.get('excerpt', '')[:500])
            slug = self.generate_slug(post['title']) or f"post-{post_id}"

            # Insert post
            self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}posts (
    ID, post_author, post_date, post_date_gmt, post_content, post_title,
    post_excerpt, post_status, comment_status, ping_status, post_password,
    post_name, to_ping, pinged, post_modified, post_modified_gmt,
    post_content_filtered, post_parent, guid, menu_order, post_type,
    post_mime_type, comment_count
) VALUES (
    {post_id}, 1, '{current_time}', '{current_time}', '{content}', '{title}',
    '{excerpt}', 'publish', 'open', 'open', '', '{slug}',
    '', '', '{current_time}', '{current_time}',
    '', 0, 'http://localhost/?p={post_id}', 0, 'post',
    '', 0
);""")

            # Assign category to post
            category_id = post.get('category_id', 1)
            if category_id in self.category_mapping:
                term_taxonomy_id = self.category_mapping[category_id]['term_taxonomy_id']

                self.sql_statements.append(f"""
INSERT INTO {self.table_prefix}term_relationships (object_id, term_taxonomy_id, term_order)
VALUES ({post_id}, {term_taxonomy_id}, 0);""")

            self.post_id_counter += 1

        print(f"✅ تم إنشاء {len(self.wp_structure['posts'])} مقال")

    def update_category_counts(self):
        """Update category post counts"""
        self.add_sql_comment("تحديث عدد المقالات في التصنيفات")

        self.sql_statements.append(f"""
UPDATE {self.table_prefix}term_taxonomy tt
SET count = (
    SELECT COUNT(*)
    FROM {self.table_prefix}term_relationships tr
    WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
)
WHERE tt.taxonomy = 'category';""")

    def generate_wordpress_options(self):
        """Generate WordPress options"""
        self.add_sql_comment("تحديث إعدادات WordPress")

        # Update site settings
        self.sql_statements.append(f"""
UPDATE {self.table_prefix}options
SET option_value = 'ar'
WHERE option_name = 'WPLANG';""")

        self.sql_statements.append(f"""
UPDATE {self.table_prefix}options
SET option_value = 'مشروع النهضة وبناء الدولة السورية'
WHERE option_name = 'blogname';""")

        self.sql_statements.append(f"""
UPDATE {self.table_prefix}options
SET option_value = 'كتاب شامل لمشروع النهضة السورية - ما بعد الاستبداد'
WHERE option_name = 'blogdescription';""")

    def generate_complete_sql(self):
        """Generate complete SQL file"""
        print("🔧 جاري إنشاء ملف SQL دقيق...")

        if not self.load_structure():
            return False

        # Add header
        self.sql_statements.append("-- Accurate WordPress SQL Import for Nahda Book")
        self.sql_statements.append("-- Generated on: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        self.sql_statements.append("-- Based on real book structure analysis")
        self.sql_statements.append("\nSET NAMES utf8mb4;")
        self.sql_statements.append("SET FOREIGN_KEY_CHECKS = 0;")

        # Generate categories
        self.generate_categories_sql()

        # Generate posts
        self.generate_posts_sql()

        # Update counts
        self.update_category_counts()

        # Update WordPress options
        self.generate_wordpress_options()

        # Add footer
        self.sql_statements.append("\nSET FOREIGN_KEY_CHECKS = 1;")
        self.sql_statements.append("-- End of accurate SQL import")

        print("✅ تم إنشاء SQL دقيق بنجاح")
        return True

    def save_sql_file(self, filename='accurate_wordpress.sql'):
        """Save SQL to file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.sql_statements))

            print(f"✅ تم حفظ ملف SQL: {filename}")

            # Show statistics
            categories_count = len(self.wp_structure['categories'])
            posts_count = len(self.wp_structure['posts'])

            print(f"📊 إحصائيات SQL الدقيق:")
            print(f"   📂 التصنيفات: {categories_count}")
            print(f"   📄 المقالات: {posts_count}")
            print(f"   📝 إجمالي الكلمات المقدرة: {posts_count * 200:,}")

            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ ملف SQL: {e}")
            return False

def main():
    """Main function"""
    print("🎯 مولد SQL دقيق لكتاب النهضة")
    print("=" * 50)

    generator = AccurateSQLGenerator()

    if generator.generate_complete_sql():
        if generator.save_sql_file():
            print("\n" + "=" * 50)
            print("🎉 تم إنشاء ملف SQL دقيق بنجاح!")
            print("📁 الملف: accurate_wordpress.sql")

            print(f"\n🎯 المميزات:")
            print(f"   ✅ بنية دقيقة مبنية على تحليل الكتاب الفعلي")
            print(f"   ✅ تصنيفات هرمية صحيحة")
            print(f"   ✅ ربط دقيق للمقالات بالتصنيفات")
            print(f"   ✅ محتوى عربي محسن")
            print(f"   ✅ أداء محسن للاستيراد")

            print(f"\n⚠️ ملاحظات:")
            print(f"   - تم تحديد المقالات إلى 1000 مقال لضمان الأداء")
            print(f"   - يمكن زيادة العدد بتعديل الكود")
            print(f"   - انشئ نسخة احتياطية قبل الاستيراد")

            return True

    return False

if __name__ == "__main__":
    main()
