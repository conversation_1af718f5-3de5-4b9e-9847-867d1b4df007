#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد المقالات الكاملة - كتاب النهضة
إنشاء مقالات كاملة بدلاً من مقاطع مقطعة
"""

from docx import Document
import re
import json
import os
from datetime import datetime
from collections import defaultdict

class CompleteArticleGenerator:
    """مولد المقالات الكاملة"""

    def __init__(self):
        self.book_structure = {
            'parts': [],      # الأقسام الرئيسية
            'chapters': [],   # الأبواب
            'sections': [],   # الفصول
            'subsections': [], # المباحث
            'articles': []    # المقالات الكاملة
        }
        self.current_article = None
        self.article_content = []

    def analyze_book_complete(self, docx_file):
        """تحليل الكتاب وإنشاء مقالات كاملة"""
        print("🔍 تحليل الكتاب لإنشاء مقالات كاملة...")

        try:
            doc = Document(docx_file)
            print(f"✅ تم تحميل الكتاب: {len(doc.paragraphs)} فقرة")

            current_part = None
            current_chapter = None
            current_section = None
            current_subsection = None

            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if not text:
                    continue

                # تحديد نوع النص
                text_type = self.classify_text(text)

                if text_type == 'part':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()

                    current_part = self.create_part(text, len(self.book_structure['parts']) + 1)
                    self.book_structure['parts'].append(current_part)
                    print(f"📚 قسم: {text[:50]}...")

                elif text_type == 'chapter':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()

                    current_chapter = self.create_chapter(text, len(self.book_structure['chapters']) + 1, current_part)
                    self.book_structure['chapters'].append(current_chapter)
                    print(f"📖 باب: {text[:50]}...")

                elif text_type == 'section':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()

                    current_section = self.create_section(text, len(self.book_structure['sections']) + 1, current_chapter)
                    self.book_structure['sections'].append(current_section)
                    print(f"📝 فصل: {text[:50]}...")

                    # بدء مقال جديد للفصل
                    self.start_new_article(text, current_section, current_chapter, current_part)

                elif text_type == 'subsection':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()

                    current_subsection = self.create_subsection(text, len(self.book_structure['subsections']) + 1, current_section)
                    self.book_structure['subsections'].append(current_subsection)
                    print(f"🔸 مبحث: {text[:50]}...")

                    # بدء مقال جديد للمبحث
                    self.start_new_article(text, current_subsection, current_section, current_chapter, current_part)

                else:
                    # محتوى المقال
                    if len(text) > 30:  # تجاهل النصوص القصيرة جداً
                        self.add_to_current_article(text)

            # حفظ آخر مقال
            self.save_current_article()

            self.print_analysis_summary()
            return True

        except Exception as e:
            print(f"❌ خطأ في تحليل الكتاب: {e}")
            return False

    def classify_text(self, text):
        """تصنيف النص حسب نوعه"""

        # الأقسام الرئيسية
        part_patterns = [
            r'^القسم\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|الحادي عشر|الثاني عشر|الثالث عشر|الرابع عشر|الخامس عشر|السادس عشر)',
            r'^الجزء\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^المرحلة\s+(الأولى|الثانية|الثالثة|الرابعة|الخامسة)',
        ]

        # الأبواب
        chapter_patterns = [
            r'^الباب\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)',
            r'^المحور\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
        ]

        # الفصول
        section_patterns = [
            r'^الفصل\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|الحادي عشر|الثاني عشر|الثالث عشر|الرابع عشر|الخامس عشر|السادس عشر|السابع عشر|الثامن عشر|التاسع عشر|العشرون|الحادي والعشرون|الثاني والعشرون|الثالث والعشرون|الرابع والعشرون|الخامس والعشرون|السادس والعشرون|السابع والعشرون|الثامن والعشرون|التاسع والعشرون|الثلاثون)',
            r'^القضية\s+(الأولى|الثانية|الثالثة|الرابعة|الخامسة)',
        ]

        # المباحث
        subsection_patterns = [
            r'^المبحث\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^أولاً[:：]',
            r'^ثانياً[:：]',
            r'^ثالثاً[:：]',
            r'^رابعاً[:：]',
            r'^خامساً[:：]',
            r'^سادساً[:：]',
        ]

        # فحص الأنماط
        for pattern in part_patterns:
            if re.search(pattern, text):
                return 'part'

        for pattern in chapter_patterns:
            if re.search(pattern, text):
                return 'chapter'

        for pattern in section_patterns:
            if re.search(pattern, text):
                return 'section'

        for pattern in subsection_patterns:
            if re.search(pattern, text):
                return 'subsection'

        return 'content'

    def create_part(self, title, part_id):
        """إنشاء قسم رئيسي"""
        return {
            'id': part_id,
            'title': title,
            'type': 'part',
            'level': 1,
            'children': []
        }

    def create_chapter(self, title, chapter_id, parent_part):
        """إنشاء باب"""
        chapter = {
            'id': chapter_id,
            'title': title,
            'type': 'chapter',
            'level': 2,
            'parent_id': parent_part['id'] if parent_part else None,
            'children': []
        }

        if parent_part:
            parent_part['children'].append(chapter_id)

        return chapter

    def create_section(self, title, section_id, parent_chapter):
        """إنشاء فصل"""
        section = {
            'id': section_id,
            'title': title,
            'type': 'section',
            'level': 3,
            'parent_id': parent_chapter['id'] if parent_chapter else None,
            'children': []
        }

        if parent_chapter:
            parent_chapter['children'].append(section_id)

        return section

    def create_subsection(self, title, subsection_id, parent_section):
        """إنشاء مبحث"""
        subsection = {
            'id': subsection_id,
            'title': title,
            'type': 'subsection',
            'level': 4,
            'parent_id': parent_section['id'] if parent_section else None,
            'children': []
        }

        if parent_section:
            parent_section['children'].append(subsection_id)

        return subsection

    def start_new_article(self, title, current_level, *parent_levels):
        """بدء مقال جديد"""
        # حفظ المقال السابق إن وجد
        self.save_current_article()

        # تحديد المستوى الأب
        parent_id = None
        parent_type = None

        if current_level:
            parent_id = current_level['id']
            parent_type = current_level['type']

        # إنشاء مقال جديد
        self.current_article = {
            'id': len(self.book_structure['articles']) + 1,
            'title': title,
            'type': 'article',
            'level': 5,
            'parent_id': parent_id,
            'parent_type': parent_type,
            'word_count': 0
        }

        self.article_content = []

        # إضافة العنوان كأول محتوى
        self.add_to_current_article(title)

    def add_to_current_article(self, text):
        """إضافة نص للمقال الحالي"""
        if self.current_article is None:
            # إنشاء مقال افتراضي إذا لم يكن هناك مقال حالي
            self.current_article = {
                'id': len(self.book_structure['articles']) + 1,
                'title': "مقدمة الكتاب",
                'type': 'article',
                'level': 5,
                'parent_id': None,
                'parent_type': None,
                'word_count': 0
            }
            self.article_content = []

        self.article_content.append(text)

        # حساب عدد الكلمات التقريبي
        words = len(text.split())
        self.current_article['word_count'] += words

    def save_current_article(self):
        """حفظ المقال الحالي"""
        if self.current_article and self.article_content:
            # دمج المحتوى
            full_content = '\n\n'.join(self.article_content)

            # تحديث المقال
            self.current_article['content'] = full_content
            self.current_article['excerpt'] = self.create_excerpt(full_content)

            # إضافة للقائمة
            self.book_structure['articles'].append(self.current_article)

            # إضافة للمستوى الأب
            if self.current_article.get('parent_type') and self.current_article.get('parent_id'):
                parent_type = self.current_article['parent_type']
                parent_id = self.current_article['parent_id']

                if parent_type == 'subsection':
                    parent = next((s for s in self.book_structure['subsections'] if s['id'] == parent_id), None)
                elif parent_type == 'section':
                    parent = next((s for s in self.book_structure['sections'] if s['id'] == parent_id), None)
                elif parent_type == 'chapter':
                    parent = next((c for c in self.book_structure['chapters'] if c['id'] == parent_id), None)
                elif parent_type == 'part':
                    parent = next((p for p in self.book_structure['parts'] if p['id'] == parent_id), None)
                else:
                    parent = None

                if parent:
                    parent['children'].append(self.current_article['id'])

            print(f"💾 حُفظ مقال: {self.current_article['title'][:50]}... ({self.current_article['word_count']} كلمة)")

            # إعادة تعيين
            self.current_article = None
            self.article_content = []

    def create_excerpt(self, content, max_length=300):
        """إنشاء مقتطف من المحتوى"""
        if len(content) <= max_length:
            return content

        # البحث عن نقطة قطع مناسبة
        excerpt = content[:max_length]
        last_sentence = excerpt.rfind('.')
        last_space = excerpt.rfind(' ')

        if last_sentence > max_length - 100:
            return content[:last_sentence + 1]
        elif last_space > max_length - 50:
            return content[:last_space] + "..."
        else:
            return content[:max_length - 3] + "..."

    def print_analysis_summary(self):
        """طباعة ملخص التحليل"""
        print(f"\n📊 ملخص تحليل الكتاب (مقالات كاملة):")
        print(f"   📚 الأقسام الرئيسية: {len(self.book_structure['parts'])}")
        print(f"   📖 الأبواب: {len(self.book_structure['chapters'])}")
        print(f"   📝 الفصول: {len(self.book_structure['sections'])}")
        print(f"   🔸 المباحث: {len(self.book_structure['subsections'])}")
        print(f"   📄 المقالات الكاملة: {len(self.book_structure['articles'])}")

        # إحصائيات الكلمات
        if self.book_structure['articles']:
            word_counts = [article['word_count'] for article in self.book_structure['articles']]
            avg_words = sum(word_counts) / len(word_counts)
            min_words = min(word_counts)
            max_words = max(word_counts)

            print(f"\n📊 إحصائيات الكلمات:")
            print(f"   📈 متوسط الكلمات: {avg_words:.0f} كلمة/مقال")
            print(f"   📉 أقل مقال: {min_words} كلمة")
            print(f"   📈 أكبر مقال: {max_words} كلمة")
            print(f"   📊 إجمالي الكلمات: {sum(word_counts):,} كلمة")

    def save_structure(self):
        """حفظ البنية في ملفات JSON"""
        # حفظ البنية الأصلية
        with open('book_structure_complete.json', 'w', encoding='utf-8') as f:
            json.dump(self.book_structure, f, ensure_ascii=False, indent=2)

        print(f"✅ تم حفظ البنية الكاملة في: book_structure_complete.json")

    def create_wordpress_structure(self):
        """إنشاء بنية WordPress للمقالات الكاملة"""
        print("\n🏗️ إنشاء بنية WordPress للمقالات الكاملة...")

        wp_categories = []
        wp_posts = []

        # إنشاء التصنيفات (نفس الطريقة السابقة)
        category_id = 1

        # الأقسام الرئيسية
        for part in self.book_structure['parts']:
            category = {
                'id': category_id,
                'name': part['title'][:80],  # تحديد الطول
                'slug': self.create_slug(part['title']),
                'parent': 0,
                'level': 1,
                'type': 'part',
                'original_id': part['id']
            }
            wp_categories.append(category)
            category_id += 1

        # الأبواب
        for chapter in self.book_structure['chapters']:
            parent_wp_id = 0
            if chapter.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == 'part' and cat['original_id'] == chapter['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']

            category = {
                'id': category_id,
                'name': chapter['title'][:80],
                'slug': self.create_slug(chapter['title']),
                'parent': parent_wp_id,
                'level': 2,
                'type': 'chapter',
                'original_id': chapter['id']
            }
            wp_categories.append(category)
            category_id += 1

        # الفصول
        for section in self.book_structure['sections']:
            parent_wp_id = 0
            if section.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == 'chapter' and cat['original_id'] == section['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']

            category = {
                'id': category_id,
                'name': section['title'][:80],
                'slug': self.create_slug(section['title']),
                'parent': parent_wp_id,
                'level': 3,
                'type': 'section',
                'original_id': section['id']
            }
            wp_categories.append(category)
            category_id += 1

        # المباحث
        for subsection in self.book_structure['subsections']:
            parent_wp_id = 0
            if subsection.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == 'section' and cat['original_id'] == subsection['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']

            category = {
                'id': category_id,
                'name': subsection['title'][:80],
                'slug': self.create_slug(subsection['title']),
                'parent': parent_wp_id,
                'level': 4,
                'type': 'subsection',
                'original_id': subsection['id']
            }
            wp_categories.append(category)
            category_id += 1

        # إنشاء المقالات الكاملة
        for article in self.book_structure['articles']:
            # تحديد التصنيف
            category_id = 1  # افتراضي

            if article.get('parent_type') and article.get('parent_id'):
                parent_cat = next((cat for cat in wp_categories
                                 if cat['type'] == article['parent_type'] and cat['original_id'] == article['parent_id']), None)
                if parent_cat:
                    category_id = parent_cat['id']

            post = {
                'id': article['id'],
                'title': article['title'][:200],  # تحديد طول العنوان
                'content': article['content'],
                'category_id': category_id,
                'slug': self.create_slug(article['title']),
                'excerpt': article.get('excerpt', ''),
                'word_count': article.get('word_count', 0)
            }
            wp_posts.append(post)

        # حفظ بنية WordPress
        wp_structure = {
            'categories': wp_categories,
            'posts': wp_posts
        }

        with open('wordpress_structure_complete.json', 'w', encoding='utf-8') as f:
            json.dump(wp_structure, f, ensure_ascii=False, indent=2)

        print(f"✅ تم إنشاء {len(wp_categories)} تصنيف و {len(wp_posts)} مقال كامل")
        return wp_structure

    def create_slug(self, text):
        """إنشاء slug للروابط"""
        # تنظيف النص
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-').lower()[:50]

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء مقالات كاملة لكتاب النهضة")
    print("=" * 60)

    # إنشاء مولد المقالات الكاملة
    generator = CompleteArticleGenerator()

    # تحليل الكتاب
    if not generator.analyze_book_complete('../Nahda.docx'):
        print("❌ فشل في تحليل الكتاب")
        return False

    # إنشاء بنية WordPress
    wp_structure = generator.create_wordpress_structure()

    # حفظ البنية
    generator.save_structure()

    print(f"\n🎉 تم تحليل الكتاب وإنشاء المقالات الكاملة بنجاح!")
    print(f"الخطوة التالية: إنشاء ملفات XML للمقالات الكاملة")

    return True, wp_structure

if __name__ == "__main__":
    main()
