#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decode Menu File and Analyze Book Structure
Creates accurate WordPress structure based on actual book content
"""

import re
import json
from docx import Document
import xml.etree.ElementTree as ET

def decode_arabic_text(text):
    """Decode Arabic text from various encodings"""
    # Try different decoding methods
    try:
        # Method 1: Direct Unicode
        if 'CDATA' in text:
            # Extract CDATA content
            match = re.search(r'<!\[CDATA\[(.*?)\]\]>', text)
            if match:
                content = match.group(1)
                # Try to decode Arabic
                try:
                    # Convert from various encodings
                    decoded = content.encode('latin1').decode('utf-8')
                    return decoded
                except:
                    return content
        return text
    except:
        return text

def parse_menu_file():
    """Parse the menu file to extract structure"""
    print("🔍 تحليل ملف التصنيفات...")
    
    try:
        with open('menuEdit.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract all CDATA sections
        cdata_pattern = r'<!\[CDATA\[(.*?)\]\]>'
        matches = re.findall(cdata_pattern, content)
        
        categories = []
        for match in matches:
            if match.strip():
                # Try to decode Arabic
                try:
                    decoded = match.encode('latin1').decode('utf-8')
                    categories.append(decoded)
                except:
                    categories.append(match)
        
        print(f"✅ تم استخراج {len(categories)} تصنيف من الملف")
        return categories
    
    except Exception as e:
        print(f"❌ خطأ في تحليل ملف التصنيفات: {e}")
        return []

def analyze_book_structure():
    """Analyze the actual book structure from DOCX"""
    print("📖 تحليل بنية الكتاب من ملف Word...")
    
    try:
        doc = Document('Nahda.docx')
        
        structure = {
            'main_parts': [],
            'chapters': [],
            'sections': [],
            'content_units': []
        }
        
        current_part = None
        current_chapter = None
        current_section = None
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if not text:
                continue
            
            # Detect main parts (الباب، القسم)
            if re.search(r'(الباب|القسم)\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس)', text):
                current_part = {
                    'id': len(structure['main_parts']) + 1,
                    'title': text,
                    'type': 'part',
                    'level': 1,
                    'paragraph_index': i
                }
                structure['main_parts'].append(current_part)
                print(f"📚 باب رئيسي: {text[:50]}...")
            
            # Detect chapters (الفصل)
            elif re.search(r'الفصل\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)', text):
                current_chapter = {
                    'id': len(structure['chapters']) + 1,
                    'title': text,
                    'type': 'chapter',
                    'level': 2,
                    'part_id': current_part['id'] if current_part else None,
                    'paragraph_index': i
                }
                structure['chapters'].append(current_chapter)
                print(f"📖 فصل: {text[:50]}...")
            
            # Detect sections (المبحث، المحور)
            elif re.search(r'(المبحث|المحور|القضية)\s+(الأول|الثاني|الثالث|الرابع|الخامس)', text):
                current_section = {
                    'id': len(structure['sections']) + 1,
                    'title': text,
                    'type': 'section',
                    'level': 3,
                    'chapter_id': current_chapter['id'] if current_chapter else None,
                    'part_id': current_part['id'] if current_part else None,
                    'paragraph_index': i
                }
                structure['sections'].append(current_section)
                print(f"📝 مبحث: {text[:50]}...")
            
            # Detect content units (longer paragraphs)
            elif len(text) > 100 and not re.search(r'^(الباب|الفصل|المبحث|المحور|القضية|خاتمة|مقدمة)', text):
                content_unit = {
                    'id': len(structure['content_units']) + 1,
                    'title': text[:100] + "..." if len(text) > 100 else text,
                    'content': text,
                    'type': 'content',
                    'level': 4,
                    'section_id': current_section['id'] if current_section else None,
                    'chapter_id': current_chapter['id'] if current_chapter else None,
                    'part_id': current_part['id'] if current_part else None,
                    'paragraph_index': i
                }
                structure['content_units'].append(content_unit)
        
        print(f"✅ تم تحليل البنية:")
        print(f"   📚 الأبواب الرئيسية: {len(structure['main_parts'])}")
        print(f"   📖 الفصول: {len(structure['chapters'])}")
        print(f"   📝 المباحث: {len(structure['sections'])}")
        print(f"   📄 وحدات المحتوى: {len(structure['content_units'])}")
        
        return structure
    
    except Exception as e:
        print(f"❌ خطأ في تحليل الكتاب: {e}")
        return None

def create_wordpress_structure(book_structure):
    """Create WordPress-compatible structure"""
    print("🔧 إنشاء بنية WordPress...")
    
    wp_structure = {
        'categories': [],
        'posts': []
    }
    
    # Create main categories (parts)
    for part in book_structure['main_parts']:
        category = {
            'id': part['id'],
            'name': part['title'],
            'slug': create_slug(part['title']),
            'parent': 0,
            'level': 1,
            'type': 'part'
        }
        wp_structure['categories'].append(category)
    
    # Create sub-categories (chapters)
    for chapter in book_structure['chapters']:
        category = {
            'id': len(wp_structure['categories']) + 1,
            'name': chapter['title'],
            'slug': create_slug(chapter['title']),
            'parent': chapter['part_id'] if chapter['part_id'] else 0,
            'level': 2,
            'type': 'chapter',
            'original_id': chapter['id']
        }
        wp_structure['categories'].append(category)
        chapter['wp_category_id'] = category['id']
    
    # Create posts from content units
    for content in book_structure['content_units']:
        # Find the appropriate category
        category_id = 1  # Default to first category
        
        if content['chapter_id']:
            # Find the WordPress category for this chapter
            for cat in wp_structure['categories']:
                if cat.get('original_id') == content['chapter_id']:
                    category_id = cat['id']
                    break
        elif content['part_id']:
            # Use the part category
            category_id = content['part_id']
        
        post = {
            'id': content['id'],
            'title': content['title'],
            'content': content['content'],
            'category_id': category_id,
            'slug': create_slug(content['title']),
            'excerpt': content['content'][:300] + "..." if len(content['content']) > 300 else content['content']
        }
        wp_structure['posts'].append(post)
    
    print(f"✅ تم إنشاء بنية WordPress:")
    print(f"   📂 التصنيفات: {len(wp_structure['categories'])}")
    print(f"   📄 المقالات: {len(wp_structure['posts'])}")
    
    return wp_structure

def create_slug(text):
    """Create URL-friendly slug"""
    # Remove special characters and create slug
    slug = re.sub(r'[^\w\s-]', '', text)
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-').lower()[:50]

def save_structure(structure, filename='accurate_book_structure.json'):
    """Save the structure to JSON file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
        print(f"✅ تم حفظ البنية في: {filename}")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ البنية: {e}")
        return False

def main():
    """Main function"""
    print("🚀 تحليل دقيق لبنية كتاب النهضة")
    print("=" * 50)
    
    # Parse menu file
    menu_categories = parse_menu_file()
    
    # Analyze book structure
    book_structure = analyze_book_structure()
    
    if book_structure:
        # Create WordPress structure
        wp_structure = create_wordpress_structure(book_structure)
        
        # Save both structures
        save_structure(book_structure, 'detailed_book_structure.json')
        save_structure(wp_structure, 'wordpress_structure.json')
        
        print("\n" + "=" * 50)
        print("✅ تم التحليل بنجاح!")
        print("📁 الملفات المُنشأة:")
        print("   - detailed_book_structure.json (البنية التفصيلية)")
        print("   - wordpress_structure.json (بنية WordPress)")
        
        print(f"\n📊 الإحصائيات النهائية:")
        print(f"   📚 الأبواب الرئيسية: {len(book_structure['main_parts'])}")
        print(f"   📖 الفصول: {len(book_structure['chapters'])}")
        print(f"   📝 المباحث: {len(book_structure['sections'])}")
        print(f"   📄 وحدات المحتوى: {len(book_structure['content_units'])}")
        print(f"   📂 تصنيفات WordPress: {len(wp_structure['categories'])}")
        print(f"   📄 مقالات WordPress: {len(wp_structure['posts'])}")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
