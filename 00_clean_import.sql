-- Clean WordPress Import - No ID Conflicts
-- This file safely imports without PRIMARY KEY conflicts
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing data (OPTIONAL - remove if you want to keep existing content)
-- DELETE FROM wp_term_relationships WHERE term_taxonomy_id > 1;
-- DELETE FROM wp_posts WHERE ID > 1;
-- DELETE FROM wp_term_taxonomy WHERE term_taxonomy_id > 1;
-- DELETE FROM wp_terms WHERE term_id > 1;

-- Reset AUTO_INCREMENT to start from safe numbers
ALTER TABLE wp_posts AUTO_INCREMENT = 10000;
ALTER TABLE wp_terms AUTO_INCREMENT = 1000;
ALTER TABLE wp_term_taxonomy AUTO_INCREMENT = 1000;

-- Now import your content using the fixed files
-- Source the fixed SQL files in order:
-- SOURCE 01_categories_fixed.sql;
-- SOURCE 02_posts_fixed.sql;
-- ... etc

SET FOREIGN_KEY_CHECKS = 1;

-- Update WordPress options
UPDATE wp_options 
SET option_value = 'مشروع النهضة وبناء الدولة السورية' 
WHERE option_name = 'blogname';

UPDATE wp_options 
SET option_value = 'كتاب شامل لمشروع النهضة السورية - ما بعد الاستبداد' 
WHERE option_name = 'blogdescription';

UPDATE wp_options 
SET option_value = 'ar' 
WHERE option_name = 'WPLANG';

-- Update category counts
UPDATE wp_term_taxonomy tt
SET count = (
    SELECT COUNT(*) 
    FROM wp_term_relationships tr 
    WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
)
WHERE tt.taxonomy = 'category';

-- End of clean import