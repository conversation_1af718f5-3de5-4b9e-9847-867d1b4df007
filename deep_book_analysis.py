#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deep Book Analysis - Accurate Hierarchical Structure
Analyzes the book content in depth to create proper tree structure
"""

import re
from docx import Document
import json

class DeepBookAnalyzer:
    """Deep analysis of book structure"""
    
    def __init__(self):
        self.structure = {
            'parts': [],      # الأقسام
            'chapters': [],   # الأبواب  
            'sections': [],   # الفصول
            'subsections': [], # المباحث/المحاور
            'articles': []    # المقالات/النصوص
        }
        self.current_part = None
        self.current_chapter = None
        self.current_section = None
        self.current_subsection = None
        
    def analyze_document(self):
        """Analyze document with deep structure recognition"""
        print("🔍 تحليل عميق لبنية الكتاب...")
        
        try:
            doc = Document('Nahda.docx')
            
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if not text:
                    continue
                
                # تحليل النص لتحديد نوعه
                self.classify_and_process_text(text, i)
            
            self.create_hierarchical_structure()
            self.print_analysis_summary()
            
            return self.structure
            
        except Exception as e:
            print(f"❌ خطأ في التحليل: {e}")
            return None
    
    def classify_and_process_text(self, text, index):
        """Classify text and process according to its type"""
        
        # تحديد الأقسام الرئيسية
        if self.is_main_part(text):
            self.process_main_part(text, index)
        
        # تحديد الأبواب
        elif self.is_chapter(text):
            self.process_chapter(text, index)
        
        # تحديد الفصول
        elif self.is_section(text):
            self.process_section(text, index)
        
        # تحديد المباحث والمحاور
        elif self.is_subsection(text):
            self.process_subsection(text, index)
        
        # النصوص والمقالات
        elif self.is_article_content(text):
            self.process_article(text, index)
    
    def is_main_part(self, text):
        """Check if text is a main part (قسم)"""
        patterns = [
            r'^القسم\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن)',
            r'^الجزء\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^المرحلة\s+(الأولى|الثانية|الثالثة)',
        ]
        return any(re.search(pattern, text) for pattern in patterns)
    
    def is_chapter(self, text):
        """Check if text is a chapter (باب)"""
        patterns = [
            r'^الباب\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)',
            r'^المحور\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
        ]
        return any(re.search(pattern, text) for pattern in patterns)
    
    def is_section(self, text):
        """Check if text is a section (فصل)"""
        patterns = [
            r'^الفصل\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)',
            r'^القضية\s+(الأولى|الثانية|الثالثة|الرابعة|الخامسة)',
        ]
        return any(re.search(pattern, text) for pattern in patterns)
    
    def is_subsection(self, text):
        """Check if text is a subsection (مبحث/محور فرعي)"""
        patterns = [
            r'^المبحث\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^المحور\s+الفرعي',
            r'^النقطة\s+(الأولى|الثانية|الثالثة)',
            r'^أولاً[:：]',
            r'^ثانياً[:：]',
            r'^ثالثاً[:：]',
            r'^رابعاً[:：]',
        ]
        return any(re.search(pattern, text) for pattern in patterns)
    
    def is_article_content(self, text):
        """Check if text is article content"""
        # نص طويل يحتوي على محتوى فعلي
        return (len(text) > 100 and 
                not self.is_main_part(text) and 
                not self.is_chapter(text) and 
                not self.is_section(text) and 
                not self.is_subsection(text))
    
    def process_main_part(self, text, index):
        """Process main part"""
        self.current_part = {
            'id': len(self.structure['parts']) + 1,
            'title': text,
            'type': 'part',
            'level': 1,
            'index': index,
            'children': []
        }
        self.structure['parts'].append(self.current_part)
        print(f"📚 قسم رئيسي: {text[:50]}...")
    
    def process_chapter(self, text, index):
        """Process chapter"""
        self.current_chapter = {
            'id': len(self.structure['chapters']) + 1,
            'title': text,
            'type': 'chapter',
            'level': 2,
            'index': index,
            'parent_id': self.current_part['id'] if self.current_part else None,
            'children': []
        }
        self.structure['chapters'].append(self.current_chapter)
        if self.current_part:
            self.current_part['children'].append(self.current_chapter['id'])
        print(f"📖 باب: {text[:50]}...")
    
    def process_section(self, text, index):
        """Process section"""
        self.current_section = {
            'id': len(self.structure['sections']) + 1,
            'title': text,
            'type': 'section',
            'level': 3,
            'index': index,
            'parent_id': self.current_chapter['id'] if self.current_chapter else None,
            'children': []
        }
        self.structure['sections'].append(self.current_section)
        if self.current_chapter:
            self.current_chapter['children'].append(self.current_section['id'])
        print(f"📝 فصل: {text[:50]}...")
    
    def process_subsection(self, text, index):
        """Process subsection"""
        self.current_subsection = {
            'id': len(self.structure['subsections']) + 1,
            'title': text,
            'type': 'subsection',
            'level': 4,
            'index': index,
            'parent_id': self.current_section['id'] if self.current_section else None,
            'children': []
        }
        self.structure['subsections'].append(self.current_subsection)
        if self.current_section:
            self.current_section['children'].append(self.current_subsection['id'])
        print(f"🔸 مبحث: {text[:50]}...")
    
    def process_article(self, text, index):
        """Process article content"""
        # تحديد العنوان من أول 100 حرف
        title = text[:100] + "..." if len(text) > 100 else text
        
        # تحديد المستوى الأب
        parent_id = None
        parent_type = None
        
        if self.current_subsection:
            parent_id = self.current_subsection['id']
            parent_type = 'subsection'
        elif self.current_section:
            parent_id = self.current_section['id']
            parent_type = 'section'
        elif self.current_chapter:
            parent_id = self.current_chapter['id']
            parent_type = 'chapter'
        elif self.current_part:
            parent_id = self.current_part['id']
            parent_type = 'part'
        
        article = {
            'id': len(self.structure['articles']) + 1,
            'title': title,
            'content': text,
            'type': 'article',
            'level': 5,
            'index': index,
            'parent_id': parent_id,
            'parent_type': parent_type
        }
        
        self.structure['articles'].append(article)
        
        # إضافة للمستوى الأب
        if self.current_subsection:
            self.current_subsection['children'].append(article['id'])
        elif self.current_section:
            self.current_section['children'].append(article['id'])
        elif self.current_chapter:
            self.current_chapter['children'].append(article['id'])
        elif self.current_part:
            self.current_part['children'].append(article['id'])
    
    def create_hierarchical_structure(self):
        """Create proper hierarchical structure for WordPress"""
        print("\n🏗️ إنشاء البنية الهرمية...")
        
        self.wp_categories = []
        self.wp_posts = []
        
        # إنشاء التصنيفات الهرمية
        self.create_wp_categories()
        
        # إنشاء المقالات
        self.create_wp_posts()
    
    def create_wp_categories(self):
        """Create WordPress categories with proper hierarchy"""
        
        # الأقسام الرئيسية (المستوى الأول)
        for part in self.structure['parts']:
            category = {
                'id': part['id'],
                'name': part['title'],
                'slug': self.create_slug(part['title']),
                'parent': 0,
                'level': 1,
                'type': 'part',
                'wp_id': part['id']
            }
            self.wp_categories.append(category)
        
        # الأبواب (المستوى الثاني)
        for chapter in self.structure['chapters']:
            category = {
                'id': len(self.wp_categories) + 1,
                'name': chapter['title'],
                'slug': self.create_slug(chapter['title']),
                'parent': chapter['parent_id'] if chapter['parent_id'] else 0,
                'level': 2,
                'type': 'chapter',
                'wp_id': len(self.wp_categories) + 1,
                'original_id': chapter['id']
            }
            self.wp_categories.append(category)
        
        # الفصول (المستوى الثالث)
        for section in self.structure['sections']:
            # العثور على الباب الأب
            parent_wp_id = 0
            if section['parent_id']:
                for cat in self.wp_categories:
                    if cat['type'] == 'chapter' and cat.get('original_id') == section['parent_id']:
                        parent_wp_id = cat['wp_id']
                        break
            
            category = {
                'id': len(self.wp_categories) + 1,
                'name': section['title'],
                'slug': self.create_slug(section['title']),
                'parent': parent_wp_id,
                'level': 3,
                'type': 'section',
                'wp_id': len(self.wp_categories) + 1,
                'original_id': section['id']
            }
            self.wp_categories.append(category)
        
        # المباحث (المستوى الرابع)
        for subsection in self.structure['subsections']:
            # العثور على الفصل الأب
            parent_wp_id = 0
            if subsection['parent_id']:
                for cat in self.wp_categories:
                    if cat['type'] == 'section' and cat.get('original_id') == subsection['parent_id']:
                        parent_wp_id = cat['wp_id']
                        break
            
            category = {
                'id': len(self.wp_categories) + 1,
                'name': subsection['title'],
                'slug': self.create_slug(subsection['title']),
                'parent': parent_wp_id,
                'level': 4,
                'type': 'subsection',
                'wp_id': len(self.wp_categories) + 1,
                'original_id': subsection['id']
            }
            self.wp_categories.append(category)
    
    def create_wp_posts(self):
        """Create WordPress posts"""
        for article in self.structure['articles']:
            # تحديد التصنيف المناسب
            category_id = 1  # افتراضي
            
            if article['parent_type'] == 'subsection':
                # البحث عن المبحث
                for cat in self.wp_categories:
                    if cat['type'] == 'subsection' and cat.get('original_id') == article['parent_id']:
                        category_id = cat['wp_id']
                        break
            elif article['parent_type'] == 'section':
                # البحث عن الفصل
                for cat in self.wp_categories:
                    if cat['type'] == 'section' and cat.get('original_id') == article['parent_id']:
                        category_id = cat['wp_id']
                        break
            elif article['parent_type'] == 'chapter':
                # البحث عن الباب
                for cat in self.wp_categories:
                    if cat['type'] == 'chapter' and cat.get('original_id') == article['parent_id']:
                        category_id = cat['wp_id']
                        break
            elif article['parent_type'] == 'part':
                # البحث عن القسم
                for cat in self.wp_categories:
                    if cat['type'] == 'part' and cat['id'] == article['parent_id']:
                        category_id = cat['wp_id']
                        break
            
            post = {
                'id': article['id'],
                'title': article['title'],
                'content': article['content'],
                'category_id': category_id,
                'slug': self.create_slug(article['title']),
                'excerpt': article['content'][:300] + "..." if len(article['content']) > 300 else article['content']
            }
            self.wp_posts.append(post)
    
    def create_slug(self, text):
        """Create URL-friendly slug"""
        # تنظيف النص وإنشاء slug
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-').lower()[:50]
    
    def print_analysis_summary(self):
        """Print analysis summary"""
        print(f"\n📊 ملخص التحليل العميق:")
        print(f"   📚 الأقسام الرئيسية: {len(self.structure['parts'])}")
        print(f"   📖 الأبواب: {len(self.structure['chapters'])}")
        print(f"   📝 الفصول: {len(self.structure['sections'])}")
        print(f"   🔸 المباحث: {len(self.structure['subsections'])}")
        print(f"   📄 المقالات: {len(self.structure['articles'])}")
        print(f"   🏷️ تصنيفات WordPress: {len(self.wp_categories)}")
        print(f"   📰 مقالات WordPress: {len(self.wp_posts)}")
    
    def save_structure(self):
        """Save the analyzed structure"""
        # حفظ البنية التفصيلية
        with open('deep_book_structure.json', 'w', encoding='utf-8') as f:
            json.dump(self.structure, f, ensure_ascii=False, indent=2)
        
        # حفظ بنية WordPress
        wp_structure = {
            'categories': self.wp_categories,
            'posts': self.wp_posts
        }
        
        with open('hierarchical_wordpress_structure.json', 'w', encoding='utf-8') as f:
            json.dump(wp_structure, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ البنية في:")
        print(f"   - deep_book_structure.json")
        print(f"   - hierarchical_wordpress_structure.json")
        
        return wp_structure

def main():
    """Main function"""
    print("🔍 تحليل عميق لبنية كتاب النهضة")
    print("=" * 60)
    
    analyzer = DeepBookAnalyzer()
    
    # تحليل الكتاب
    structure = analyzer.analyze_document()
    
    if structure:
        # حفظ البنية
        wp_structure = analyzer.save_structure()
        
        print(f"\n🎯 البنية الهرمية الصحيحة:")
        print(f"   المستوى 1: القسم (Part)")
        print(f"   المستوى 2: الباب (Chapter)")
        print(f"   المستوى 3: الفصل (Section)")
        print(f"   المستوى 4: المبحث (Subsection)")
        print(f"   المستوى 5: المقال (Article)")
        
        return wp_structure
    
    return None

if __name__ == "__main__":
    main()
