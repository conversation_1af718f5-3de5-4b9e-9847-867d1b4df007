
# تعليمات استيراد المحتوى إلى WordPress

## المتطلبات الأساسية:
1. موقع WordPress يدعم اللغة العربية
2. إضافة WordPress Importer مُفعلة
3. ذا<PERSON>رة PHP كافية (يُنصح بـ 512MB أو أكثر)
4. وقت تنفيذ PHP مناسب (300 ثانية أو أكثر)

## خطوات الاستيراد:

### 1. تحضير WordPress:
```
- تأكد من تفعيل اللغة العربية في Settings > General
- قم بتثبيت قالب يدعم RTL (Right-to-Left)
- تأكد من تفعيل إضافة WordPress Importer
```

### 2. استيراد المحتوى:
```
1. اذهب إلى Tools > Import
2. اختر "WordPress"
3. ارفع ملف wordpress_import.xml
4. اختر المؤلف أو أنشئ مؤلف جديد
5. تأك<PERSON> من تحديد "Download and import file attachments"
6. اضغط "Submit"
```

### 3. بعد الاستيراد:
```
- تحقق من التصنيفات في Posts > Categories
- تأكد من ظهور المقالات في Posts > All Posts
- اختبر عرض المحتوى العربي
- تحقق من البنية الهرمية للتصنيفات
```

## استكشاف الأخطاء:

### مشكلة: فشل الاستيراد
```
الحل: زيادة memory_limit و max_execution_time في PHP
```

### مشكلة: النص العربي يظهر كرموز غريبة
```
الحل: تأكد من أن قاعدة البيانات تستخدم utf8mb4_unicode_ci
```

### مشكلة: التصنيفات لا تظهر بشكل هرمي
```
الحل: تحقق من إعدادات القالب ودعمه للتصنيفات الهرمية
```

## تحسينات إضافية:

### 1. تحسين SEO:
```
- استخدم إضافة Yoast SEO أو RankMath
- أضف meta descriptions للمقالات
- تأكد من بنية URLs صديقة للمحركات
```

### 2. تحسين التنقل:
```
- أنشئ قائمة تنقل تعكس بنية الكتاب
- أضف widget للتصنيفات في الشريط الجانبي
- استخدم breadcrumbs للتنقل
```

### 3. تحسين العرض:
```
- استخدم قالب يدعم الطباعة العربية بشكل جيد
- أضف خطوط عربية مناسبة
- تأكد من محاذاة النص من اليمين لليسار
```
