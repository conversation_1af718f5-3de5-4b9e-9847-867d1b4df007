# 📚 مشروع النهضة - استيراد WordPress

## 🎯 نظرة عامة
تم تحليل كتاب "مشروع النهضة وبناء الدولة السورية" بدقة وإنشاء ملفات SQL محسنة لاستيراده إلى WordPress مع الحفاظ على البنية الهرمية الأصلية.

## 📊 إحصائيات المحتوى
- **📚 الأقسام الرئيسية:** 16 قسم
- **📂 الأبواب:** 200 باب
- **📖 الفصول:** 328 فصل  
- **📝 المباحث:** 5 مباحث
- **📄 وحدات المحتوى:** 4,938 وحدة
- **🏷️ تصنيفات WordPress:** 528 تصنيف هرمي
- **📰 مقالات WordPress:** 4,938 مقال

## 📁 الملفات المتوفرة

### 🧪 للاختبار السريع:
- **`test_nahda_sample.sql`** (0.1 MB)
  - 30 تصنيف + 100 مقال
  - مثالي للاختبار السريع
  - وقت الاستيراد: 1-3 دقائق

### 🔧 للاستيراد المحسن (مُوصى به):
- **`01_categories.sql`** (0.17 MB) - جميع التصنيفات الهرمية
- **`02_posts.sql`** إلى **`11_posts.sql`** (0.65-0.77 MB لكل ملف) - المقالات مقسمة
- **`99_setup.sql`** (0.01 MB) - الإعدادات النهائية

### 📦 للاستيراد الشامل:
- **`accurate_wordpress.sql`** (1.71 MB) - ملف واحد شامل

## 🚀 خطوات الاستيراد

### 🧪 الطريقة الأولى: الاختبار السريع

```bash
# 1. انشئ نسخة احتياطية
mysqldump -u username -p database_name > backup.sql

# 2. استورد نسخة الاختبار
mysql -u username -p database_name < test_nahda_sample.sql
```

### ⚡ الطريقة الثانية: الاستيراد المحسن (مُوصى به)

```bash
# 1. انشئ نسخة احتياطية
mysqldump -u username -p database_name > backup.sql

# 2. استورد التصنيفات
mysql -u username -p database_name < 01_categories.sql

# 3. استورد المقالات (بالترتيب)
mysql -u username -p database_name < 02_posts.sql
mysql -u username -p database_name < 03_posts.sql
mysql -u username -p database_name < 04_posts.sql
mysql -u username -p database_name < 05_posts.sql
mysql -u username -p database_name < 06_posts.sql
mysql -u username -p database_name < 07_posts.sql
mysql -u username -p database_name < 08_posts.sql
mysql -u username -p database_name < 09_posts.sql
mysql -u username -p database_name < 10_posts.sql
mysql -u username -p database_name < 11_posts.sql

# 4. طبق الإعدادات النهائية
mysql -u username -p database_name < 99_setup.sql
```

### 🔄 الاستيراد التلقائي (Linux/Mac):
```bash
#!/bin/bash
for file in 01_categories.sql 02_posts.sql 03_posts.sql 04_posts.sql 05_posts.sql 06_posts.sql 07_posts.sql 08_posts.sql 09_posts.sql 10_posts.sql 11_posts.sql 99_setup.sql; do
    echo "استيراد $file..."
    mysql -u username -p database_name < "$file"
    echo "تم الانتهاء من $file"
done
```

### 🔄 الاستيراد التلقائي (Windows):
```cmd
@echo off
for %%f in (01_categories.sql 02_posts.sql 03_posts.sql 04_posts.sql 05_posts.sql 06_posts.sql 07_posts.sql 08_posts.sql 09_posts.sql 10_posts.sql 11_posts.sql 99_setup.sql) do (
    echo استيراد %%f...
    mysql -u username -p database_name < "%%f"
    echo تم الانتهاء من %%f
)
```

## 🌐 الاستيراد عبر phpMyAdmin

### للملفات الصغيرة:
1. ادخل إلى phpMyAdmin
2. اختر قاعدة البيانات
3. اذهب إلى تبويب "استيراد"
4. اختر الملف
5. اضغط "تنفيذ"

### للملفات الكبيرة:
- قد تحتاج لزيادة `upload_max_filesize` و `post_max_size` في PHP
- أو استخدم سطر الأوامر

## ⚙️ متطلبات النظام

### قاعدة البيانات:
- MySQL 5.7+ أو MariaDB 10.2+
- ترميز UTF-8 (utf8mb4_unicode_ci)
- مساحة فارغة: 50-100 MB

### PHP (للملفات الكبيرة):
```ini
memory_limit = 512M
max_execution_time = 300
upload_max_filesize = 100M
post_max_size = 100M
```

### WordPress:
- WordPress 5.0+
- دعم اللغة العربية
- مساحة تخزين: 100+ MB

## 🔍 التحقق من نجاح الاستيراد

### 1. التصنيفات:
- اذهب إلى **المقالات** → **التصنيفات**
- تحقق من وجود 528 تصنيف
- تحقق من البنية الهرمية

### 2. المقالات:
- اذهب إلى **المقالات** → **جميع المقالات**
- تحقق من وجود 4,938 مقال (أو 100 في نسخة الاختبار)
- تحقق من ربط المقالات بالتصنيفات

### 3. الإعدادات:
- تحقق من تغيير عنوان الموقع إلى "مشروع النهضة وبناء الدولة السورية"
- تحقق من تغيير وصف الموقع

## 🛠️ استكشاف الأخطاء

### خطأ في الترميز:
```sql
ALTER DATABASE database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### خطأ في الذاكرة:
- استخدم الملفات المقسمة بدلاً من الملف الشامل
- زد `memory_limit` في PHP

### خطأ في الصلاحيات:
```sql
GRANT ALL PRIVILEGES ON database_name.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### خطأ في حجم الملف:
- استخدم سطر الأوامر بدلاً من phpMyAdmin
- أو قسم الملفات إلى أجزاء أصغر

## 📈 الأداء والتحسين

### بعد الاستيراد:
```sql
-- تحسين الجداول
OPTIMIZE TABLE wp_posts;
OPTIMIZE TABLE wp_terms;
OPTIMIZE TABLE wp_term_taxonomy;
OPTIMIZE TABLE wp_term_relationships;

-- إعادة بناء الفهارس
REPAIR TABLE wp_posts;
REPAIR TABLE wp_terms;
```

### إعدادات WordPress:
- فعل التخزين المؤقت
- استخدم CDN للملفات الثابتة
- حسن قاعدة البيانات دورياً

## 🎯 التوصيات

### للمواقع الجديدة:
✅ استخدم **الملفات المقسمة** (01_categories.sql + 02-11_posts.sql + 99_setup.sql)

### للاختبار:
✅ ابدأ بـ **test_nahda_sample.sql**

### للمواقع الموجودة:
⚠️ انشئ **نسخة احتياطية كاملة** قبل الاستيراد

### للأداء الأمثل:
✅ استخدم **سطر الأوامر** للملفات الكبيرة
✅ استورد في **أوقات قليلة الزيارات**
✅ راقب **استهلاك الذاكرة** أثناء الاستيراد

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `test_instructions.md` للاختبار
2. تحقق من ملف `multiple_files_instructions.md` للاستيراد المتقدم
3. تأكد من متطلبات النظام
4. انشئ نسخة احتياطية دائماً

---

**🎉 بالتوفيق في استيراد مشروع النهضة إلى WordPress!**
