#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد ملفات XML للاستيراد - كتاب النهضة
إنشاء ملفات XML محسنة للاستيراد في WordPress
"""

import docx
import re
import json
import os
from datetime import datetime
from collections import defaultdict

class NahdaXMLGenerator:
    """مولد ملفات XML لكتاب النهضة"""
    
    def __init__(self):
        self.book_structure = {
            'parts': [],      # الأقسام الرئيسية
            'chapters': [],   # الأبواب
            'sections': [],   # الفصول
            'subsections': [], # المباحث
            'articles': []    # المقالات
        }
        self.wp_categories = []
        self.wp_posts = []
        
    def analyze_book(self, docx_file):
        """تحليل الكتاب واستخراج البنية"""
        print("🔍 تحليل كتاب النهضة...")
        
        try:
            doc = docx.Document(docx_file)
            print(f"✅ تم تحميل الكتاب: {len(doc.paragraphs)} فقرة")
            
            current_part = None
            current_chapter = None
            current_section = None
            current_subsection = None
            article_content = []
            
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if not text:
                    continue
                
                # تحديد نوع النص
                text_type = self.classify_text(text)
                
                if text_type == 'part':
                    # حفظ المقال السابق إن وجد
                    if article_content:
                        self.save_article(article_content, current_subsection, current_section, current_chapter, current_part)
                        article_content = []
                    
                    current_part = self.create_part(text, len(self.book_structure['parts']) + 1)
                    self.book_structure['parts'].append(current_part)
                    print(f"📚 قسم: {text[:50]}...")
                    
                elif text_type == 'chapter':
                    # حفظ المقال السابق إن وجد
                    if article_content:
                        self.save_article(article_content, current_subsection, current_section, current_chapter, current_part)
                        article_content = []
                    
                    current_chapter = self.create_chapter(text, len(self.book_structure['chapters']) + 1, current_part)
                    self.book_structure['chapters'].append(current_chapter)
                    print(f"📖 باب: {text[:50]}...")
                    
                elif text_type == 'section':
                    # حفظ المقال السابق إن وجد
                    if article_content:
                        self.save_article(article_content, current_subsection, current_section, current_chapter, current_part)
                        article_content = []
                    
                    current_section = self.create_section(text, len(self.book_structure['sections']) + 1, current_chapter)
                    self.book_structure['sections'].append(current_section)
                    print(f"📝 فصل: {text[:50]}...")
                    
                elif text_type == 'subsection':
                    # حفظ المقال السابق إن وجد
                    if article_content:
                        self.save_article(article_content, current_subsection, current_section, current_chapter, current_part)
                        article_content = []
                    
                    current_subsection = self.create_subsection(text, len(self.book_structure['subsections']) + 1, current_section)
                    self.book_structure['subsections'].append(current_subsection)
                    print(f"🔸 مبحث: {text[:50]}...")
                    
                else:
                    # محتوى المقال
                    if len(text) > 50:  # تجاهل النصوص القصيرة جداً
                        article_content.append(text)
                        
                        # إذا وصل المحتوى لحجم مناسب، احفظه كمقال
                        if len(' '.join(article_content)) > 1000:
                            self.save_article(article_content, current_subsection, current_section, current_chapter, current_part)
                            article_content = []
            
            # حفظ آخر مقال
            if article_content:
                self.save_article(article_content, current_subsection, current_section, current_chapter, current_part)
            
            self.print_analysis_summary()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحليل الكتاب: {e}")
            return False
    
    def classify_text(self, text):
        """تصنيف النص حسب نوعه"""
        
        # الأقسام الرئيسية
        part_patterns = [
            r'^القسم\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن)',
            r'^الجزء\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^المرحلة\s+(الأولى|الثانية|الثالثة|الرابعة|الخامسة)',
        ]
        
        # الأبواب
        chapter_patterns = [
            r'^الباب\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)',
            r'^المحور\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
        ]
        
        # الفصول
        section_patterns = [
            r'^الفصل\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)',
            r'^القضية\s+(الأولى|الثانية|الثالثة|الرابعة|الخامسة)',
        ]
        
        # المباحث
        subsection_patterns = [
            r'^المبحث\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^أولاً[:：]',
            r'^ثانياً[:：]',
            r'^ثالثاً[:：]',
            r'^رابعاً[:：]',
            r'^خامساً[:：]',
        ]
        
        # فحص الأنماط
        for pattern in part_patterns:
            if re.search(pattern, text):
                return 'part'
        
        for pattern in chapter_patterns:
            if re.search(pattern, text):
                return 'chapter'
        
        for pattern in section_patterns:
            if re.search(pattern, text):
                return 'section'
        
        for pattern in subsection_patterns:
            if re.search(pattern, text):
                return 'subsection'
        
        return 'content'
    
    def create_part(self, title, part_id):
        """إنشاء قسم رئيسي"""
        return {
            'id': part_id,
            'title': title,
            'type': 'part',
            'level': 1,
            'children': []
        }
    
    def create_chapter(self, title, chapter_id, parent_part):
        """إنشاء باب"""
        chapter = {
            'id': chapter_id,
            'title': title,
            'type': 'chapter',
            'level': 2,
            'parent_id': parent_part['id'] if parent_part else None,
            'children': []
        }
        
        if parent_part:
            parent_part['children'].append(chapter_id)
        
        return chapter
    
    def create_section(self, title, section_id, parent_chapter):
        """إنشاء فصل"""
        section = {
            'id': section_id,
            'title': title,
            'type': 'section',
            'level': 3,
            'parent_id': parent_chapter['id'] if parent_chapter else None,
            'children': []
        }
        
        if parent_chapter:
            parent_chapter['children'].append(section_id)
        
        return section
    
    def create_subsection(self, title, subsection_id, parent_section):
        """إنشاء مبحث"""
        subsection = {
            'id': subsection_id,
            'title': title,
            'type': 'subsection',
            'level': 4,
            'parent_id': parent_section['id'] if parent_section else None,
            'children': []
        }
        
        if parent_section:
            parent_section['children'].append(subsection_id)
        
        return subsection
    
    def save_article(self, content_list, subsection, section, chapter, part):
        """حفظ مقال"""
        if not content_list:
            return
        
        # تحديد العنوان
        title = content_list[0][:100] if content_list else "مقال بدون عنوان"
        if len(title) > 100:
            title = title[:97] + "..."
        
        # دمج المحتوى
        full_content = '\n\n'.join(content_list)
        
        # تحديد المستوى الأب
        parent_id = None
        parent_type = None
        
        if subsection:
            parent_id = subsection['id']
            parent_type = 'subsection'
        elif section:
            parent_id = section['id']
            parent_type = 'section'
        elif chapter:
            parent_id = chapter['id']
            parent_type = 'chapter'
        elif part:
            parent_id = part['id']
            parent_type = 'part'
        
        article = {
            'id': len(self.book_structure['articles']) + 1,
            'title': title,
            'content': full_content,
            'type': 'article',
            'level': 5,
            'parent_id': parent_id,
            'parent_type': parent_type
        }
        
        self.book_structure['articles'].append(article)
        
        # إضافة للمستوى الأب
        if subsection:
            subsection['children'].append(article['id'])
        elif section:
            section['children'].append(article['id'])
        elif chapter:
            chapter['children'].append(article['id'])
        elif part:
            part['children'].append(article['id'])
    
    def print_analysis_summary(self):
        """طباعة ملخص التحليل"""
        print(f"\n📊 ملخص تحليل الكتاب:")
        print(f"   📚 الأقسام الرئيسية: {len(self.book_structure['parts'])}")
        print(f"   📖 الأبواب: {len(self.book_structure['chapters'])}")
        print(f"   📝 الفصول: {len(self.book_structure['sections'])}")
        print(f"   🔸 المباحث: {len(self.book_structure['subsections'])}")
        print(f"   📄 المقالات: {len(self.book_structure['articles'])}")
    
    def create_wordpress_structure(self):
        """إنشاء بنية WordPress"""
        print("\n🏗️ إنشاء بنية WordPress...")
        
        self.wp_categories = []
        self.wp_posts = []
        
        # إنشاء التصنيفات
        category_id = 1
        
        # الأقسام الرئيسية
        for part in self.book_structure['parts']:
            category = {
                'id': category_id,
                'name': part['title'],
                'slug': self.create_slug(part['title']),
                'parent': 0,
                'level': 1,
                'type': 'part',
                'original_id': part['id']
            }
            self.wp_categories.append(category)
            category_id += 1
        
        # الأبواب
        for chapter in self.book_structure['chapters']:
            parent_wp_id = 0
            if chapter.get('parent_id'):
                parent_cat = next((cat for cat in self.wp_categories 
                                 if cat['type'] == 'part' and cat['original_id'] == chapter['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']
            
            category = {
                'id': category_id,
                'name': chapter['title'],
                'slug': self.create_slug(chapter['title']),
                'parent': parent_wp_id,
                'level': 2,
                'type': 'chapter',
                'original_id': chapter['id']
            }
            self.wp_categories.append(category)
            category_id += 1
        
        # الفصول
        for section in self.book_structure['sections']:
            parent_wp_id = 0
            if section.get('parent_id'):
                parent_cat = next((cat for cat in self.wp_categories 
                                 if cat['type'] == 'chapter' and cat['original_id'] == section['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']
            
            category = {
                'id': category_id,
                'name': section['title'],
                'slug': self.create_slug(section['title']),
                'parent': parent_wp_id,
                'level': 3,
                'type': 'section',
                'original_id': section['id']
            }
            self.wp_categories.append(category)
            category_id += 1
        
        # المباحث
        for subsection in self.book_structure['subsections']:
            parent_wp_id = 0
            if subsection.get('parent_id'):
                parent_cat = next((cat for cat in self.wp_categories 
                                 if cat['type'] == 'section' and cat['original_id'] == subsection['parent_id']), None)
                if parent_cat:
                    parent_wp_id = parent_cat['id']
            
            category = {
                'id': category_id,
                'name': subsection['title'],
                'slug': self.create_slug(subsection['title']),
                'parent': parent_wp_id,
                'level': 4,
                'type': 'subsection',
                'original_id': subsection['id']
            }
            self.wp_categories.append(category)
            category_id += 1
        
        # إنشاء المقالات
        for article in self.book_structure['articles']:
            # تحديد التصنيف
            category_id = 1  # افتراضي
            
            if article.get('parent_type') and article.get('parent_id'):
                parent_cat = next((cat for cat in self.wp_categories 
                                 if cat['type'] == article['parent_type'] and cat['original_id'] == article['parent_id']), None)
                if parent_cat:
                    category_id = parent_cat['id']
            
            post = {
                'id': article['id'],
                'title': article['title'],
                'content': article['content'],
                'category_id': category_id,
                'slug': self.create_slug(article['title']),
                'excerpt': article['content'][:300] + "..." if len(article['content']) > 300 else article['content']
            }
            self.wp_posts.append(post)
        
        print(f"✅ تم إنشاء {len(self.wp_categories)} تصنيف و {len(self.wp_posts)} مقال")
    
    def create_slug(self, text):
        """إنشاء slug للروابط"""
        # تنظيف النص
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-').lower()[:50]
    
    def save_structure(self):
        """حفظ البنية في ملفات JSON"""
        # حفظ البنية الأصلية
        with open('book_structure_detailed.json', 'w', encoding='utf-8') as f:
            json.dump(self.book_structure, f, ensure_ascii=False, indent=2)
        
        # حفظ بنية WordPress
        wp_structure = {
            'categories': self.wp_categories,
            'posts': self.wp_posts
        }
        
        with open('wordpress_structure_ready.json', 'w', encoding='utf-8') as f:
            json.dump(wp_structure, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ البنية في ملفات JSON")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء ملفات استيراد كتاب النهضة")
    print("=" * 60)
    
    # إنشاء مولد XML
    generator = NahdaXMLGenerator()
    
    # تحليل الكتاب
    if not generator.analyze_book('../Nahda.docx'):
        print("❌ فشل في تحليل الكتاب")
        return False
    
    # إنشاء بنية WordPress
    generator.create_wordpress_structure()
    
    # حفظ البنية
    generator.save_structure()
    
    print(f"\n🎉 تم تحليل الكتاب بنجاح!")
    print(f"الخطوة التالية: إنشاء ملفات XML للاستيراد")
    
    return True

if __name__ == "__main__":
    main()
