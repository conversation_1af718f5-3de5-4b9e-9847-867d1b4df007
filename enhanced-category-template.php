<?php
/**
 * قالب محسن لصفحات التصنيفات
 * Template Name: تصنيف النهضة المحسن
 */

get_header(); 

$category = get_queried_object();
$category_level = nahda_get_category_level($category);
$category_icon = nahda_get_level_icon($category_level);
?>

<div class="nahda-enhanced-category-container">
    
    <!-- مسار التنقل -->
    <div class="nahda-breadcrumb-wrapper">
        <?php echo do_shortcode('[nahda_breadcrumb]'); ?>
    </div>
    
    <!-- رأس التصنيف -->
    <header class="enhanced-category-header">
        <div class="category-info">
            <div class="category-level-badge level-<?php echo $category_level; ?>">
                <?php echo $category_icon; ?> 
                <?php echo nahda_get_level_name($category_level); ?>
            </div>
            
            <h1 class="category-title"><?php echo $category->name; ?></h1>
            
            <?php if ($category->description): ?>
                <div class="category-description">
                    <?php echo wpautop($category->description); ?>
                </div>
            <?php endif; ?>
            
            <div class="category-stats">
                <div class="stat-item">
                    <span class="stat-icon">📄</span>
                    <span class="stat-text"><?php echo $category->count; ?> مقال</span>
                </div>
                
                <?php
                $subcategories = get_categories(array(
                    'parent' => $category->term_id,
                    'hide_empty' => false
                ));
                if (!empty($subcategories)):
                ?>
                <div class="stat-item">
                    <span class="stat-icon">📂</span>
                    <span class="stat-text"><?php echo count($subcategories); ?> قسم فرعي</span>
                </div>
                <?php endif; ?>
                
                <div class="stat-item">
                    <span class="stat-icon">📊</span>
                    <span class="stat-text">المستوى <?php echo $category_level; ?></span>
                </div>
            </div>
        </div>
        
        <!-- أدوات التصنيف -->
        <div class="category-tools">
            <div class="view-options">
                <button onclick="nahda_switch_view('list')" class="view-btn active" id="list-view">📋 قائمة</button>
                <button onclick="nahda_switch_view('grid')" class="view-btn" id="grid-view">⊞ شبكة</button>
                <button onclick="nahda_switch_view('detailed')" class="view-btn" id="detailed-view">📖 تفصيلي</button>
            </div>
            
            <div class="sort-options">
                <select onchange="nahda_sort_posts(this.value)" class="sort-select">
                    <option value="date_asc">ترتيب حسب التاريخ (الأقدم أولاً)</option>
                    <option value="date_desc">ترتيب حسب التاريخ (الأحدث أولاً)</option>
                    <option value="title_asc">ترتيب أبجدي (أ-ي)</option>
                    <option value="title_desc">ترتيب أبجدي (ي-أ)</option>
                </select>
            </div>
        </div>
    </header>
    
    <div class="category-content-wrapper">
        
        <!-- المحتوى الرئيسي -->
        <main class="category-main-content">
            
            <!-- الأقسام الفرعية -->
            <?php if (!empty($subcategories)): ?>
            <section class="subcategories-section">
                <h2>📂 الأقسام الفرعية</h2>
                <div class="subcategories-grid">
                    <?php foreach ($subcategories as $subcat): 
                        $sub_level = nahda_get_category_level($subcat);
                        $sub_icon = nahda_get_level_icon($sub_level);
                    ?>
                    <div class="subcategory-card">
                        <div class="subcat-header">
                            <span class="subcat-icon"><?php echo $sub_icon; ?></span>
                            <h3><a href="<?php echo get_category_link($subcat->term_id); ?>"><?php echo $subcat->name; ?></a></h3>
                        </div>
                        <div class="subcat-stats">
                            <span>📄 <?php echo $subcat->count; ?> مقال</span>
                        </div>
                        <?php if ($subcat->description): ?>
                        <div class="subcat-description">
                            <?php echo wp_trim_words($subcat->description, 15); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>
            
            <!-- المقالات -->
            <section class="posts-section">
                <h2>📄 المقالات (<?php echo $category->count; ?>)</h2>
                
                <?php if (have_posts()): ?>
                <div class="posts-container" id="posts-container">
                    <?php while (have_posts()): the_post(); ?>
                    
                    <article class="enhanced-post-item" data-post-id="<?php the_ID(); ?>">
                        
                        <!-- رأس المقال -->
                        <header class="post-item-header">
                            <h3 class="post-item-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>
                            
                            <div class="post-item-meta">
                                <span class="post-date">📅 <?php echo get_the_date('Y/m/d'); ?></span>
                                <span class="post-words">📊 <?php echo nahda_count_words(get_the_content()); ?> كلمة</span>
                                <span class="reading-time">⏱️ <?php echo nahda_reading_time(get_the_content()); ?> دقيقة</span>
                            </div>
                        </header>
                        
                        <!-- محتوى المقال -->
                        <div class="post-item-content">
                            <?php
                            $content = get_the_content();
                            $enhanced_content = nahda_format_archive_content($content);
                            echo $enhanced_content;
                            ?>
                        </div>
                        
                        <!-- أدوات المقال -->
                        <footer class="post-item-footer">
                            <div class="post-actions">
                                <a href="<?php the_permalink(); ?>" class="read-full-btn">📖 قراءة كاملة</a>
                                <button onclick="nahda_save_post(<?php the_ID(); ?>)" class="save-btn">💾 حفظ</button>
                                <button onclick="nahda_share_post(<?php the_ID(); ?>)" class="share-btn">📤 مشاركة</button>
                            </div>
                            
                            <div class="post-categories">
                                <?php
                                $post_categories = get_the_category();
                                foreach ($post_categories as $cat) {
                                    if ($cat->term_id != $category->term_id) {
                                        echo '<span class="additional-cat"><a href="' . get_category_link($cat->term_id) . '">' . $cat->name . '</a></span>';
                                    }
                                }
                                ?>
                            </div>
                        </footer>
                        
                    </article>
                    
                    <?php endwhile; ?>
                </div>
                
                <!-- التنقل بين الصفحات -->
                <div class="posts-pagination">
                    <?php
                    echo paginate_links(array(
                        'prev_text' => '← السابق',
                        'next_text' => 'التالي →',
                        'type' => 'list'
                    ));
                    ?>
                </div>
                
                <?php else: ?>
                <div class="no-posts-message">
                    <h3>📭 لا توجد مقالات في هذا القسم حالياً</h3>
                    <p>يمكنك تصفح الأقسام الفرعية أو العودة إلى <a href="<?php echo home_url('/nahda-book-map/'); ?>">خارطة الكتاب</a>.</p>
                </div>
                <?php endif; ?>
                
            </section>
            
        </main>
        
        <!-- الشريط الجانبي -->
        <aside class="category-sidebar">
            
            <!-- فهرس سريع -->
            <div class="sidebar-widget">
                <h4>🗂️ فهرس سريع</h4>
                <?php echo nahda_get_category_tree_widget($category->term_id); ?>
            </div>
            
            <!-- إحصائيات التصنيف -->
            <div class="sidebar-widget">
                <h4>📊 إحصائيات القسم</h4>
                <div class="category-detailed-stats">
                    <?php echo nahda_get_detailed_category_stats($category->term_id); ?>
                </div>
            </div>
            
            <!-- المقالات الأكثر قراءة -->
            <div class="sidebar-widget">
                <h4>🔥 الأكثر قراءة</h4>
                <?php echo nahda_get_popular_posts_in_category($category->term_id); ?>
            </div>
            
            <!-- أدوات مفيدة -->
            <div class="sidebar-widget">
                <h4>🔧 أدوات مفيدة</h4>
                <div class="useful-tools">
                    <button onclick="nahda_export_category(<?php echo $category->term_id; ?>)" class="tool-btn">📥 تصدير القسم</button>
                    <button onclick="nahda_print_category()" class="tool-btn">🖨️ طباعة</button>
                    <button onclick="nahda_bookmark_category(<?php echo $category->term_id; ?>)" class="tool-btn">🔖 إضافة للمفضلة</button>
                </div>
            </div>
            
        </aside>
        
    </div>
    
</div>

<!-- أنماط CSS محسنة -->
<style>
.nahda-enhanced-category-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    direction: rtl;
}

.enhanced-category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.category-info {
    text-align: center;
    margin-bottom: 30px;
}

.category-level-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9em;
    font-weight: 600;
    margin-bottom: 15px;
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.category-title {
    font-size: 2.5em;
    margin: 15px 0;
    font-weight: 700;
}

.category-description {
    font-size: 1.1em;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto 20px;
    line-height: 1.6;
}

.category-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.1);
    padding: 10px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.category-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.view-options {
    display: flex;
    gap: 10px;
}

.view-btn {
    padding: 8px 16px;
    border: 2px solid rgba(255,255,255,0.3);
    background: transparent;
    color: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active,
.view-btn:hover {
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.sort-select {
    padding: 8px 15px;
    border: 2px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.category-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
}

.subcategories-section {
    margin-bottom: 40px;
}

.subcategories-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.subcategories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.subcategory-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-right: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.subcategory-card:hover {
    transform: translateY(-5px);
}

.subcat-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.subcat-icon {
    font-size: 1.2em;
}

.subcat-header h3 {
    margin: 0;
    font-size: 1.1em;
}

.subcat-header a {
    color: #2c3e50;
    text-decoration: none;
}

.subcat-header a:hover {
    color: #3498db;
}

.posts-section h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.5em;
}

.posts-container {
    display: grid;
    gap: 25px;
}

/* عرض القائمة */
.posts-container.list-view .enhanced-post-item {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-right: 4px solid #3498db;
}

/* عرض الشبكة */
.posts-container.grid-view {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.posts-container.grid-view .enhanced-post-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    height: fit-content;
}

/* العرض التفصيلي */
.posts-container.detailed-view .enhanced-post-item {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    border-right: 5px solid #3498db;
}

.post-item-header {
    margin-bottom: 15px;
}

.post-item-title {
    margin: 0 0 10px 0;
    font-size: 1.3em;
}

.post-item-title a {
    color: #2c3e50;
    text-decoration: none;
}

.post-item-title a:hover {
    color: #3498db;
}

.post-item-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    color: #7f8c8d;
    font-size: 0.85em;
}

.post-item-content {
    margin: 15px 0;
    line-height: 1.6;
    color: #2c3e50;
}

.post-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ecf0f1;
}

.post-actions {
    display: flex;
    gap: 10px;
}

.read-full-btn,
.save-btn,
.share-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.85em;
    text-decoration: none;
    transition: transform 0.2s ease;
}

.read-full-btn {
    background: #3498db;
    color: white;
}

.save-btn {
    background: #2ecc71;
    color: white;
}

.share-btn {
    background: #e74c3c;
    color: white;
}

.read-full-btn:hover,
.save-btn:hover,
.share-btn:hover {
    transform: translateY(-2px);
}

.category-sidebar {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.sidebar-widget {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.sidebar-widget:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.sidebar-widget h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.useful-tools {
    display: grid;
    gap: 10px;
}

.tool-btn {
    padding: 10px 15px;
    border: none;
    background: #3498db;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.2s ease;
}

.tool-btn:hover {
    background: #2980b9;
}

.no-posts-message {
    text-align: center;
    padding: 40px;
    background: #f8f9fa;
    border-radius: 10px;
    color: #6c757d;
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .nahda-enhanced-category-container {
        padding: 10px;
    }
    
    .enhanced-category-header {
        padding: 20px;
    }
    
    .category-title {
        font-size: 1.8em;
    }
    
    .category-content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .category-tools {
        flex-direction: column;
        align-items: stretch;
    }
    
    .subcategories-grid {
        grid-template-columns: 1fr;
    }
    
    .posts-container.grid-view {
        grid-template-columns: 1fr;
    }
    
    .post-item-footer {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
}
</style>

<!-- JavaScript للتفاعل -->
<script>
function nahda_switch_view(viewType) {
    const container = document.getElementById('posts-container');
    const buttons = document.querySelectorAll('.view-btn');
    
    // إزالة الكلاسات السابقة
    container.className = 'posts-container';
    buttons.forEach(btn => btn.classList.remove('active'));
    
    // إضافة الكلاس الجديد
    container.classList.add(viewType + '-view');
    document.getElementById(viewType + '-view').classList.add('active');
}

function nahda_sort_posts(sortType) {
    // هذه دالة يمكن تطويرها مع AJAX
    console.log('Sorting by:', sortType);
}

function nahda_save_post(postId) {
    // حفظ المقال
    alert('تم حفظ المقال رقم ' + postId);
}

function nahda_share_post(postId) {
    // مشاركة المقال
    if (navigator.share) {
        navigator.share({
            title: document.querySelector('[data-post-id="' + postId + '"] .post-item-title a').textContent,
            url: document.querySelector('[data-post-id="' + postId + '"] .read-full-btn').href
        });
    } else {
        alert('تم نسخ رابط المقال');
    }
}

function nahda_export_category(categoryId) {
    alert('سيتم تصدير القسم رقم ' + categoryId);
}

function nahda_print_category() {
    window.print();
}

function nahda_bookmark_category(categoryId) {
    alert('تم إضافة القسم للمفضلة');
}
</script>

<?php
// دوال مساعدة إضافية
function nahda_get_level_name($level) {
    $names = array(
        1 => 'قسم رئيسي',
        2 => 'باب',
        3 => 'فصل',
        4 => 'مبحث',
        5 => 'مقال'
    );
    
    return isset($names[$level]) ? $names[$level] : 'مستوى ' . $level;
}

function nahda_format_archive_content($content) {
    // تنسيق محتوى الأرشيف
    $words = explode(' ', strip_tags($content));
    if (count($words) > 50) {
        $content = implode(' ', array_slice($words, 0, 50)) . '...';
    }
    
    return wpautop($content);
}

function nahda_get_category_tree_widget($category_id) {
    // إنشاء شجرة التصنيفات للويدجت
    $parent = get_category($category_id);
    $children = get_categories(array(
        'parent' => $category_id,
        'hide_empty' => false
    ));
    
    $output = '<ul class="category-tree-widget">';
    
    if ($parent->parent != 0) {
        $grandparent = get_category($parent->parent);
        $output .= '<li class="parent-category"><a href="' . get_category_link($grandparent->term_id) . '">↑ ' . $grandparent->name . '</a></li>';
    }
    
    $output .= '<li class="current-category">📍 ' . $parent->name . '</li>';
    
    foreach ($children as $child) {
        $output .= '<li class="child-category"><a href="' . get_category_link($child->term_id) . '">→ ' . $child->name . '</a></li>';
    }
    
    $output .= '</ul>';
    
    return $output;
}

function nahda_get_detailed_category_stats($category_id) {
    $posts = get_posts(array(
        'category' => $category_id,
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    $total_words = 0;
    foreach ($posts as $post) {
        $total_words += str_word_count(strip_tags($post->post_content));
    }
    
    $stats = '';
    $stats .= '<div class="stat-row">📄 إجمالي المقالات: ' . count($posts) . '</div>';
    $stats .= '<div class="stat-row">📊 إجمالي الكلمات: ' . number_format($total_words) . '</div>';
    $stats .= '<div class="stat-row">⏱️ وقت القراءة: ' . ceil($total_words / 200) . ' دقيقة</div>';
    
    return $stats;
}

function nahda_get_popular_posts_in_category($category_id) {
    $posts = get_posts(array(
        'category' => $category_id,
        'posts_per_page' => 5,
        'meta_key' => 'views',
        'orderby' => 'meta_value_num',
        'order' => 'DESC'
    ));
    
    if (empty($posts)) {
        $posts = get_posts(array(
            'category' => $category_id,
            'posts_per_page' => 5,
            'orderby' => 'comment_count',
            'order' => 'DESC'
        ));
    }
    
    $output = '<ul class="popular-posts-list">';
    foreach ($posts as $post) {
        $output .= '<li><a href="' . get_permalink($post->ID) . '">' . get_the_title($post->ID) . '</a></li>';
    }
    $output .= '</ul>';
    
    return $output;
}

get_footer(); ?>
