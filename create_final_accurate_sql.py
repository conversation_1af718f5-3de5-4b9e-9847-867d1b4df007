#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Accurate SQL Generator
Creates optimized SQL files with proper hierarchical structure
"""

from accurate_sql_generator import AccurateSQLGenerator
import json
import os

def create_optimized_sql():
    """Create optimized SQL with proper structure"""
    print("🚀 إنشاء ملف SQL نهائي ودقيق")
    print("=" * 60)
    
    # Load structure to check size
    try:
        with open('wordpress_structure.json', 'r', encoding='utf-8') as f:
            structure = json.load(f)
    except:
        print("❌ لم يتم العثور على ملف البنية")
        return False
    
    print("📊 تحليل المحتوى:")
    print(f"   📂 إجمالي التصنيفات: {len(structure['categories'])}")
    print(f"   📄 إجمالي المقالات: {len(structure['posts'])}")
    
    # Ask user for preference
    print(f"\n🎯 خيارات الإنشاء:")
    print(f"   1. ملف واحد شامل (قد يكون كبير)")
    print(f"   2. ملفات متعددة مُحسنة (مُوصى به)")
    print(f"   3. نسخة مُختصرة للاختبار (1000 مقال)")
    
    choice = input(f"\n❓ اختر الخيار (1/2/3): ").strip()
    
    if choice == "1":
        return create_single_file()
    elif choice == "2":
        return create_multiple_files()
    elif choice == "3":
        return create_test_version()
    else:
        print("❌ خيار غير صحيح")
        return False

def create_single_file():
    """Create single comprehensive SQL file"""
    print("\n🔧 إنشاء ملف SQL واحد شامل...")
    
    generator = AccurateSQLGenerator()
    
    if generator.generate_complete_sql():
        if generator.save_sql_file('complete_nahda_book.sql'):
            create_single_file_instructions()
            return True
    return False

def create_multiple_files():
    """Create multiple optimized SQL files"""
    print("\n🔧 إنشاء ملفات SQL متعددة مُحسنة...")
    
    # Load structure
    with open('wordpress_structure.json', 'r', encoding='utf-8') as f:
        structure = json.load(f)
    
    # Create categories file
    create_categories_file(structure)
    
    # Create posts files (split into chunks)
    create_posts_files(structure)
    
    # Create setup file
    create_setup_file()
    
    # Create instructions
    create_multiple_files_instructions()
    
    return True

def create_categories_file(structure):
    """Create categories-only SQL file"""
    print("📂 إنشاء ملف التصنيفات...")
    
    generator = AccurateSQLGenerator()
    generator.wp_structure = structure
    
    # Generate only categories
    generator.sql_statements = []
    generator.sql_statements.append("-- WordPress Categories for Nahda Book")
    generator.sql_statements.append("-- Part 1: Categories and Taxonomy")
    generator.sql_statements.append("SET NAMES utf8mb4;")
    generator.sql_statements.append("SET FOREIGN_KEY_CHECKS = 0;")
    
    generator.generate_categories_sql()
    
    generator.sql_statements.append("SET FOREIGN_KEY_CHECKS = 1;")
    generator.sql_statements.append("-- End of categories import")
    
    # Save categories file
    with open('01_categories.sql', 'w', encoding='utf-8') as f:
        f.write('\n'.join(generator.sql_statements))
    
    print(f"✅ تم إنشاء ملف التصنيفات: 01_categories.sql")

def create_posts_files(structure):
    """Create posts files in chunks"""
    print("📄 إنشاء ملفات المقالات...")
    
    posts = structure['posts']
    chunk_size = 500  # 500 posts per file
    
    for i in range(0, len(posts), chunk_size):
        chunk = posts[i:i + chunk_size]
        file_num = (i // chunk_size) + 2  # Start from 02
        
        create_posts_chunk_file(chunk, file_num, structure)
        print(f"✅ تم إنشاء ملف المقالات {file_num:02d}: {file_num:02d}_posts.sql ({len(chunk)} مقال)")

def create_posts_chunk_file(posts_chunk, file_num, structure):
    """Create a single posts chunk file"""
    generator = AccurateSQLGenerator()
    generator.wp_structure = {'posts': posts_chunk, 'categories': structure['categories']}
    
    # Load category mapping (simulate the categories creation)
    generator.category_mapping = {}
    for i, category in enumerate(structure['categories']):
        generator.category_mapping[category['id']] = {
            'term_taxonomy_id': 100 + i
        }
    
    # Generate posts SQL
    generator.sql_statements = []
    generator.sql_statements.append(f"-- WordPress Posts for Nahda Book - Part {file_num}")
    generator.sql_statements.append(f"-- Posts {(file_num-2)*500 + 1} to {(file_num-2)*500 + len(posts_chunk)}")
    generator.sql_statements.append("SET NAMES utf8mb4;")
    generator.sql_statements.append("SET FOREIGN_KEY_CHECKS = 0;")
    
    # Set starting post ID
    generator.post_id_counter = (file_num - 2) * 500 + 1
    
    generator.generate_posts_sql()
    
    generator.sql_statements.append("SET FOREIGN_KEY_CHECKS = 1;")
    generator.sql_statements.append(f"-- End of posts part {file_num}")
    
    # Save file
    with open(f'{file_num:02d}_posts.sql', 'w', encoding='utf-8') as f:
        f.write('\n'.join(generator.sql_statements))

def create_setup_file():
    """Create WordPress setup file"""
    print("⚙️ إنشاء ملف الإعدادات...")
    
    setup_sql = """-- WordPress Setup for Nahda Book
-- Final setup and optimizations
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Update WordPress options
UPDATE wp_options 
SET option_value = 'ar' 
WHERE option_name = 'WPLANG';

UPDATE wp_options 
SET option_value = 'مشروع النهضة وبناء الدولة السورية' 
WHERE option_name = 'blogname';

UPDATE wp_options 
SET option_value = 'كتاب شامل لمشروع النهضة السورية - ما بعد الاستبداد' 
WHERE option_name = 'blogdescription';

-- Update category counts
UPDATE wp_term_taxonomy tt
SET count = (
    SELECT COUNT(*) 
    FROM wp_term_relationships tr 
    WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
)
WHERE tt.taxonomy = 'category';

-- Optimize tables
OPTIMIZE TABLE wp_posts;
OPTIMIZE TABLE wp_terms;
OPTIMIZE TABLE wp_term_taxonomy;
OPTIMIZE TABLE wp_term_relationships;

SET FOREIGN_KEY_CHECKS = 1;
-- End of setup"""
    
    with open('99_setup.sql', 'w', encoding='utf-8') as f:
        f.write(setup_sql)
    
    print(f"✅ تم إنشاء ملف الإعدادات: 99_setup.sql")

def create_test_version():
    """Create test version with limited content"""
    print("\n🧪 إنشاء نسخة اختبار...")
    
    # Load structure
    with open('wordpress_structure.json', 'r', encoding='utf-8') as f:
        structure = json.load(f)
    
    # Limit to first 50 categories and 200 posts
    limited_structure = {
        'categories': structure['categories'][:50],
        'posts': structure['posts'][:200]
    }
    
    generator = AccurateSQLGenerator()
    generator.wp_structure = limited_structure
    
    if generator.generate_complete_sql():
        if generator.save_sql_file('test_nahda_sample.sql'):
            create_test_instructions()
            return True
    return False

def create_single_file_instructions():
    """Create instructions for single file"""
    instructions = """
# تعليمات استيراد الملف الشامل

## الملف: complete_nahda_book.sql

### المحتوى:
- 528 تصنيف هرمي
- 4938 مقال كامل
- إعدادات WordPress

### خطوات الاستيراد:
1. انشئ نسخة احتياطية
2. تأكد من إعدادات PHP (memory_limit = 1024M)
3. استورد عبر phpMyAdmin أو سطر الأوامر
4. انتظر 15-30 دقيقة للاكتمال

### الأمر:
```bash
mysql -u username -p database_name < complete_nahda_book.sql
```
"""
    
    with open('single_file_instructions.md', 'w', encoding='utf-8') as f:
        f.write(instructions)

def create_multiple_files_instructions():
    """Create instructions for multiple files"""
    instructions = """
# تعليمات استيراد الملفات المتعددة

## الملفات المُنشأة:
- 01_categories.sql - التصنيفات (528 تصنيف)
- 02_posts.sql إلى XX_posts.sql - المقالات (500 مقال لكل ملف)
- 99_setup.sql - الإعدادات النهائية

## خطوات الاستيراد:

### 1. استيراد التصنيفات:
```bash
mysql -u username -p database_name < 01_categories.sql
```

### 2. استيراد المقالات (بالترتيب):
```bash
mysql -u username -p database_name < 02_posts.sql
mysql -u username -p database_name < 03_posts.sql
# ... وهكذا لجميع ملفات المقالات
```

### 3. تطبيق الإعدادات النهائية:
```bash
mysql -u username -p database_name < 99_setup.sql
```

## استيراد تلقائي (Linux/Mac):
```bash
for file in *.sql; do
    echo "Importing $file..."
    mysql -u username -p database_name < "$file"
done
```

## استيراد تلقائي (Windows):
```cmd
for %f in (*.sql) do mysql -u username -p database_name < "%f"
```

## المميزات:
✅ استيراد تدريجي آمن
✅ إمكانية إيقاف واستكمال
✅ ملفات أصغر وأسرع
✅ أقل استهلاك للذاكرة
"""
    
    with open('multiple_files_instructions.md', 'w', encoding='utf-8') as f:
        f.write(instructions)

def create_test_instructions():
    """Create instructions for test version"""
    instructions = """
# تعليمات نسخة الاختبار

## الملف: test_nahda_sample.sql

### المحتوى:
- 50 تصنيف
- 200 مقال
- مثالي للاختبار

### الاستيراد:
```bash
mysql -u username -p database_name < test_nahda_sample.sql
```

### الوقت المتوقع: 2-5 دقائق
"""
    
    with open('test_instructions.md', 'w', encoding='utf-8') as f:
        f.write(instructions)

def main():
    """Main function"""
    result = create_optimized_sql()
    
    if result:
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء ملفات SQL بنجاح!")
        
        # List created files
        sql_files = [f for f in os.listdir('.') if f.endswith('.sql')]
        if sql_files:
            print("📁 الملفات المُنشأة:")
            for file in sorted(sql_files):
                size = os.path.getsize(file) / (1024 * 1024)  # MB
                print(f"   - {file} ({size:.2f} MB)")
        
        print(f"\n🎯 التوصية:")
        print(f"   - للمواقع الجديدة: استخدم الملفات المتعددة")
        print(f"   - للاختبار: استخدم نسخة الاختبار")
        print(f"   - انشئ نسخة احتياطية دائماً")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
