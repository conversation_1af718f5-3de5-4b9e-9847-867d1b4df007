#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple WordPress XML Creator
Creates a clean, valid WordPress XML file
"""

import json
from datetime import datetime

def create_simple_wordpress_xml():
    """Create a simple, valid WordPress XML file"""
    
    # Load structure
    with open('book_structure.json', 'r', encoding='utf-8') as f:
        structure = json.load(f)
    
    # Create XML content as string (to avoid namespace issues)
    xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0"
    xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:wfw="http://wellformedweb.org/CommentAPI/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:wp="http://wordpress.org/export/1.2/">

<channel>
    <title>مشروع النهضة وبناء الدولة السورية</title>
    <link>http://localhost</link>
    <description>كتاب مشروع النهضة وبناء الدولة السورية - ما بعد الاستبداد</description>
    <pubDate>{pubdate}</pubDate>
    <language>ar</language>
    <wp:wxr_version>1.2</wp:wxr_version>
    <wp:base_site_url>http://localhost</wp:base_site_url>
    <wp:base_blog_url>http://localhost</wp:base_blog_url>

'''.format(pubdate=datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000'))

    # Add categories (first 10 parts only)
    category_id = 1
    for part in structure['parts'][:10]:
        xml_content += f'''    <wp:category>
        <wp:term_id>{category_id}</wp:term_id>
        <wp:category_nicename>part-{part['id']}</wp:category_nicename>
        <wp:category_parent></wp:category_parent>
        <wp:cat_name><![CDATA[{part['title'][:80]}]]></wp:cat_name>
    </wp:category>

'''
        category_id += 1

    # Add chapters as sub-categories (first 20 chapters)
    for chapter in structure['chapters'][:20]:
        if chapter.get('part_id') and chapter['part_id'] <= 10:
            xml_content += f'''    <wp:category>
        <wp:term_id>{category_id}</wp:term_id>
        <wp:category_nicename>chapter-{chapter['id']}</wp:category_nicename>
        <wp:category_parent>part-{chapter['part_id']}</wp:category_parent>
        <wp:cat_name><![CDATA[{chapter['title'][:80]}]]></wp:cat_name>
    </wp:category>

'''
            category_id += 1

    # Add posts (first 30 content units)
    post_id = 1
    for content_unit in structure['content'][:30]:
        title = content_unit['title'][:100] if len(content_unit['title']) > 100 else content_unit['title']
        
        # Build content
        content_text = ""
        for item in content_unit.get('content', [])[:3]:  # Max 3 items per post
            if item['type'] == 'content':
                text = item['text'][:800]  # Limit text
                content_text += f"<p>{escape_html(text)}</p>\n"
        
        # Find category
        category_nicename = "uncategorized"
        if content_unit.get('chapter_id') and content_unit['chapter_id'] <= 20:
            chapter = next((c for c in structure['chapters'] if c['id'] == content_unit['chapter_id']), None)
            if chapter and chapter.get('part_id') and chapter['part_id'] <= 10:
                category_nicename = f"chapter-{chapter['id']}"
        
        xml_content += f'''    <item>
        <title><![CDATA[{title}]]></title>
        <link>http://localhost/?p={post_id}</link>
        <pubDate>{datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}</pubDate>
        <dc:creator><![CDATA[admin]]></dc:creator>
        <guid isPermaLink="false">http://localhost/?p={post_id}</guid>
        <description></description>
        <content:encoded><![CDATA[{content_text}]]></content:encoded>
        <wp:post_id>{post_id}</wp:post_id>
        <wp:post_date>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date>
        <wp:post_date_gmt>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date_gmt>
        <wp:comment_status>open</wp:comment_status>
        <wp:ping_status>open</wp:ping_status>
        <wp:post_name>post-{post_id}</wp:post_name>
        <wp:status>publish</wp:status>
        <wp:post_parent>0</wp:post_parent>
        <wp:menu_order>0</wp:menu_order>
        <wp:post_type>post</wp:post_type>
        <wp:post_password></wp:post_password>
        <wp:is_sticky>0</wp:is_sticky>
        <category domain="category" nicename="{category_nicename}"><![CDATA[فصل]]></category>
    </item>

'''
        post_id += 1

    xml_content += '''</channel>
</rss>'''

    # Save file
    with open('wordpress_simple.xml', 'w', encoding='utf-8') as f:
        f.write(xml_content)
    
    file_size = len(xml_content.encode('utf-8')) / (1024 * 1024)
    print(f"✅ تم إنشاء wordpress_simple.xml ({file_size:.2f} MB)")
    print(f"📊 المحتوى: 10 أبواب، 20 فصل، 30 مقال")

def escape_html(text):
    """Escape HTML characters"""
    return (text.replace('&', '&amp;')
                .replace('<', '&lt;')
                .replace('>', '&gt;')
                .replace('"', '&quot;')
                .replace("'", '&#39;'))

if __name__ == "__main__":
    print("🔧 إنشاء ملف WordPress XML بسيط وصحيح")
    create_simple_wordpress_xml()
    print("✅ تم الانتهاء! جرب استيراد wordpress_simple.xml")
