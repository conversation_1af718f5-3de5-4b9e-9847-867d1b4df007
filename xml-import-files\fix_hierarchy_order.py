#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح ترتيب التصنيفات الهرمية - كتاب النهضة
ضمان استيراد التصنيفات الأب قبل الفرعية
"""

import json
import re
from datetime import datetime

class HierarchyOrderFixer:
    """مصحح ترتيب التصنيفات الهرمية"""

    def __init__(self):
        self.wp_categories = []
        self.wp_posts = []
        self.category_id_counter = 1
        self.post_id_counter = 1
        self.category_mapping = {}

    def load_organized_structure(self):
        """تحميل البنية المنظمة"""
        try:
            with open('book_structure_organized.json', 'r', encoding='utf-8') as f:
                self.book_structure = json.load(f)
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False

    def create_hierarchical_categories_ordered(self):
        """إنشاء التصنيفات بترتيب هرمي صحيح"""
        print("🔧 إنشاء التصنيفات بترتيب هرمي صحيح...")

        # المرحلة 1: إنشاء تصنيف المقدمة (مستوى 0)
        if self.book_structure['introduction']:
            intro_cat = self.create_category(
                name='مقدمة الكتاب',
                slug='introduction',
                parent_id=0
            )
            self.wp_categories.append(intro_cat)
            self.category_mapping['introduction'] = intro_cat['id']
            print(f"📚 مقدمة: {intro_cat['name']} (ID: {intro_cat['id']})")

        # المرحلة 2: إنشاء الأقسام الرئيسية (مستوى 1)
        part_mapping = {}
        for part in self.book_structure['parts']:
            part_slug = self.create_slug(part['title'])
            part_cat = self.create_category(
                name=self.clean_title(part['title']),
                slug=part_slug,
                parent_id=0
            )
            self.wp_categories.append(part_cat)
            part_mapping[part['id']] = part_cat['id']
            self.category_mapping[part_slug] = part_cat['id']
            print(f"📚 قسم: {part_cat['name']} (ID: {part_cat['id']})")

        # المرحلة 3: إنشاء الأبواب تحت الأقسام (مستوى 2)
        chapter_mapping = {}
        for chapter in self.book_structure['chapters']:
            chapter_slug = self.create_slug(chapter['title'])
            parent_id = 0

            if chapter.get('parent_part_id') and chapter['parent_part_id'] in part_mapping:
                parent_id = part_mapping[chapter['parent_part_id']]

            chapter_cat = self.create_category(
                name=self.clean_title(chapter['title']),
                slug=chapter_slug,
                parent_id=parent_id
            )
            self.wp_categories.append(chapter_cat)
            chapter_mapping[chapter['id']] = chapter_cat['id']
            self.category_mapping[chapter_slug] = chapter_cat['id']

            parent_name = self.get_category_name_by_id(parent_id) if parent_id > 0 else "لا يوجد"
            print(f"  📖 باب: {chapter_cat['name']} (ID: {chapter_cat['id']}, الأب: {parent_name})")

        # المرحلة 4: إنشاء المقالات
        for section in self.book_structure['sections']:
            category_id = self.category_mapping.get('introduction', 1)  # افتراضي للمقدمة

            if section.get('parent_chapter_id') and section['parent_chapter_id'] in chapter_mapping:
                category_id = chapter_mapping[section['parent_chapter_id']]
            elif section.get('parent_part_id') and section['parent_part_id'] in part_mapping:
                category_id = part_mapping[section['parent_part_id']]

            section_post = self.create_post(
                title=self.clean_title(section['title']),
                content=section['html_content'],
                category_id=category_id,
                word_count=section['word_count']
            )
            self.wp_posts.append(section_post)

        print(f"✅ تم إنشاء البنية الهرمية المرتبة:")
        print(f"   📂 التصنيفات: {len(self.wp_categories)}")
        print(f"   📄 المقالات: {len(self.wp_posts)}")

    def create_category(self, name, slug, parent_id):
        """إنشاء تصنيف"""
        category = {
            'id': self.category_id_counter,
            'name': name,
            'slug': slug,
            'parent_id': parent_id
        }
        self.category_id_counter += 1
        return category

    def create_post(self, title, content, category_id, word_count):
        """إنشاء مقال"""
        post = {
            'id': self.post_id_counter,
            'title': title,
            'content': content,
            'category_id': category_id,
            'slug': self.create_slug(title),
            'excerpt': self.create_excerpt(content),
            'word_count': word_count
        }
        self.post_id_counter += 1
        return post

    def get_category_name_by_id(self, category_id):
        """الحصول على اسم التصنيف بالـ ID"""
        for cat in self.wp_categories:
            if cat['id'] == category_id:
                return cat['name']
        return 'غير محدد'

    def clean_title(self, title):
        """تنظيف العنوان"""
        cleaned = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', '', title)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned[:100]

    def create_slug(self, text):
        """إنشاء slug للروابط"""
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-').lower()[:50]

    def create_excerpt(self, content, max_length=300):
        """إنشاء مقتطف من المحتوى"""
        text_content = re.sub(r'<[^>]+>', '', content)

        if len(text_content) <= max_length:
            return text_content

        excerpt = text_content[:max_length]
        last_sentence = excerpt.rfind('.')
        last_space = excerpt.rfind(' ')

        if last_sentence > max_length - 100:
            return text_content[:last_sentence + 1]
        elif last_space > max_length - 50:
            return text_content[:last_space] + "..."
        else:
            return text_content[:max_length - 3] + "..."

    def create_xml_header(self):
        """إنشاء رأس ملف XML"""
        return '''<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0"
    xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:wfw="http://wellformedweb.org/CommentAPI/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:wp="http://wordpress.org/export/1.2/">

<channel>
    <title>كتاب النهضة - البنية الهرمية المرتبة</title>
    <link>http://localhost</link>
    <description>مشروع النهضة وبناء الدولة السورية - ترتيب هرمي صحيح</description>
    <pubDate>{}</pubDate>
    <language>ar</language>
    <wp:wxr_version>1.2</wp:wxr_version>
    <wp:base_site_url>http://localhost</wp:base_site_url>
    <wp:base_blog_url>http://localhost</wp:base_blog_url>

'''.format(datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000'))

    def create_category_xml(self, category):
        """إنشاء XML للتصنيف"""
        return f'''    <wp:category>
        <wp:term_id>{category['id']}</wp:term_id>
        <wp:category_nicename>{category['slug']}</wp:category_nicename>
        <wp:category_parent>{category['parent_id']}</wp:category_parent>
        <wp:cat_name><![CDATA[{category['name']}]]></wp:cat_name>
    </wp:category>

'''

    def create_post_xml(self, post):
        """إنشاء XML للمقال"""
        category_name = self.get_category_name_by_id(post['category_id'])
        category_slug = self.get_category_slug_by_id(post['category_id'])

        return f'''    <item>
        <title><![CDATA[{post['title']}]]></title>
        <link>http://localhost/{post['slug']}/</link>
        <pubDate>{datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}</pubDate>
        <dc:creator><![CDATA[admin]]></dc:creator>
        <guid isPermaLink="false">http://localhost/?p={post['id']}</guid>
        <description></description>
        <content:encoded><![CDATA[{post['content']}]]></content:encoded>
        <excerpt:encoded><![CDATA[{post['excerpt']}]]></excerpt:encoded>
        <wp:post_id>{post['id']}</wp:post_id>
        <wp:post_date>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date>
        <wp:post_date_gmt>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date_gmt>
        <wp:comment_status>open</wp:comment_status>
        <wp:ping_status>open</wp:ping_status>
        <wp:post_name>{post['slug']}</wp:post_name>
        <wp:status>publish</wp:status>
        <wp:post_parent>0</wp:post_parent>
        <wp:menu_order>0</wp:menu_order>
        <wp:post_type>post</wp:post_type>
        <wp:post_password></wp:post_password>
        <wp:is_sticky>0</wp:is_sticky>
        <category domain="category" nicename="{category_slug}"><![CDATA[{category_name}]]></category>
    </item>

'''

    def get_category_slug_by_id(self, category_id):
        """الحصول على slug التصنيف بالـ ID"""
        for cat in self.wp_categories:
            if cat['id'] == category_id:
                return cat['slug']
        return 'unknown'

    def create_xml_footer(self):
        """إنشاء ذيل ملف XML"""
        return '''</channel>
</rss>'''

    def generate_ordered_categories_file(self):
        """إنشاء ملف التصنيفات بترتيب صحيح"""
        print("📂 إنشاء ملف التصنيفات بترتيب هرمي صحيح...")

        xml_content = self.create_xml_header()

        # إضافة التصنيفات بالترتيب الصحيح (الآباء أولاً)
        for category in self.wp_categories:
            xml_content += self.create_category_xml(category)

        xml_content += self.create_xml_footer()

        filename = 'ordered_hierarchy_categories.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)

        file_size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB - {len(self.wp_categories)} تصنيف)")

        return filename

    def generate_test_file(self):
        """إنشاء ملف اختبار"""
        print("🧪 إنشاء ملف اختبار للترتيب الهرمي...")

        # اختيار عينة من التصنيفات والمقالات
        test_categories = self.wp_categories[:20]
        test_posts = self.wp_posts[:10]

        xml_content = self.create_xml_header()

        # إضافة التصنيفات بالترتيب
        for category in test_categories:
            xml_content += self.create_category_xml(category)

        # إضافة المقالات
        for post in test_posts:
            xml_content += self.create_post_xml(post)

        xml_content += self.create_xml_footer()

        filename = 'test_ordered_hierarchy.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)

        file_size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB)")
        print(f"   📊 المحتوى: {len(test_categories)} تصنيف + {len(test_posts)} مقال")

        return filename

    def generate_posts_files(self, max_file_size_mb=2):
        """إنشاء ملفات المقالات مع ربطها بالتصنيفات المناسبة"""
        print("📄 إنشاء ملفات المقالات مع ربطها بالتصنيفات المناسبة...")

        max_file_size = max_file_size_mb * 1024 * 1024  # تحويل إلى بايت
        files_created = []

        current_file_posts = []
        current_file_size = 0
        file_number = 1

        header_size = len(self.create_xml_header().encode('utf-8'))
        footer_size = len(self.create_xml_footer().encode('utf-8'))

        for post in self.wp_posts:
            post_xml = self.create_post_xml(post)
            post_size = len(post_xml.encode('utf-8'))

            # تحقق من حجم الملف
            if current_file_size + post_size + header_size + footer_size > max_file_size and current_file_posts:
                # حفظ الملف الحالي
                filename = self.save_posts_file(current_file_posts, file_number)
                files_created.append(filename)

                # بدء ملف جديد
                current_file_posts = [post]
                current_file_size = post_size
                file_number += 1
            else:
                current_file_posts.append(post)
                current_file_size += post_size

        # حفظ آخر ملف
        if current_file_posts:
            filename = self.save_posts_file(current_file_posts, file_number)
            files_created.append(filename)

        return files_created

    def save_posts_file(self, posts, file_number):
        """حفظ ملف المقالات"""
        xml_content = self.create_xml_header()

        for post in posts:
            xml_content += self.create_post_xml(post)

        xml_content += self.create_xml_footer()

        filename = f'ordered_posts_part_{file_number:02d}.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)

        file_size = os.path.getsize(filename) / 1024
        avg_words = sum(post['word_count'] for post in posts) // len(posts) if posts else 0

        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB - {len(posts)} مقال)")
        print(f"   📊 متوسط الكلمات: {avg_words} كلمة/مقال")

        # طباعة عينة من المقالات وتصنيفاتها
        print(f"   📋 عينة من المقالات:")
        for post in posts[:3]:
            category_name = self.get_category_name_by_id(post['category_id'])
            print(f"      📄 {post['title'][:40]}... → {category_name}")

        return filename

    def print_hierarchy_analysis(self):
        """طباعة تحليل البنية الهرمية"""
        print(f"\n📊 تحليل البنية الهرمية المرتبة:")

        # تحليل مستويات التصنيفات
        level_0_cats = [cat for cat in self.wp_categories if cat['parent_id'] == 0]
        level_1_cats = [cat for cat in self.wp_categories if cat['parent_id'] > 0]

        print(f"   📚 المستوى الأول (أقسام رئيسية): {len(level_0_cats)}")
        print(f"   📖 المستوى الثاني (أبواب فرعية): {len(level_1_cats)}")
        print(f"   📄 المقالات: {len(self.wp_posts)}")

        # عرض عينة من البنية
        print(f"\n🌳 عينة من البنية الهرمية المرتبة:")
        for level_0_cat in level_0_cats[:3]:
            print(f"📚 {level_0_cat['name']} (ID: {level_0_cat['id']})")

            # البحث عن الأبواب التابعة لهذا القسم
            child_cats = [cat for cat in self.wp_categories if cat['parent_id'] == level_0_cat['id']]
            for child_cat in child_cats[:3]:
                print(f"  📖 {child_cat['name']} (ID: {child_cat['id']}, الأب: {level_0_cat['id']})")

                # البحث عن المقالات التابعة لهذا الباب
                child_posts = [post for post in self.wp_posts if post['category_id'] == child_cat['id']]
                for post in child_posts[:2]:
                    print(f"    📄 {post['title'][:50]}...")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح ترتيب التصنيفات الهرمية لكتاب النهضة")
    print("=" * 70)
    print("🎯 الهدف: ضمان استيراد التصنيفات الأب قبل الفرعية")
    print("📋 الطريقة: ترتيب التصنيفات حسب المستوى الهرمي")
    print("🔍 النتيجة: Parent Category يظهر بشكل صحيح في WordPress")
    print("=" * 70)

    # إنشاء مصحح الترتيب الهرمي
    fixer = HierarchyOrderFixer()

    # تحميل البنية المنظمة
    if not fixer.load_organized_structure():
        return False

    # إنشاء التصنيفات بترتيب هرمي صحيح
    fixer.create_hierarchical_categories_ordered()

    # طباعة تحليل البنية
    fixer.print_hierarchy_analysis()

    print(f"\n🔧 إنشاء ملفات XML بترتيب هرمي صحيح...")

    # إنشاء ملف الاختبار
    test_file = fixer.generate_test_file()

    # إنشاء ملف التصنيفات المرتب
    categories_file = fixer.generate_ordered_categories_file()

    # إنشاء ملفات المقالات
    posts_files = fixer.generate_posts_files()

    print(f"\n🎉 تم إصلاح ترتيب التصنيفات الهرمية وإنشاء المقالات بنجاح!")
    print(f"📁 الملفات المُنشأة: {1 + 1 + len(posts_files)} ملف")
    print(f"📂 التصنيفات المرتبة: {len(fixer.wp_categories)}")
    print(f"📄 المقالات: {len(fixer.wp_posts)}")

    print(f"\n💡 تعليمات الاستيراد:")
    print(f"1. استورد {categories_file} أولاً (التصنيفات)")
    print(f"2. ثم استورد ملفات المقالات:")
    for posts_file in posts_files:
        print(f"   - {posts_file}")
    print(f"3. تحقق من Parent Category في WordPress")
    print(f"4. تحقق من ربط المقالات بالتصنيفات المناسبة")

    return True

if __name__ == "__main__":
    import os
    main()
