<?php
/**
 * إصلاح عرض المحتوى الكامل مع التنسيق
 * حل مشكلة المقالات المقطوعة وعدم التنسيق
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

class NahdaContentFixer {
    
    public function __construct() {
        // إصلاح عرض المحتوى
        add_filter('the_content', array($this, 'enhance_content_display'));
        add_filter('the_excerpt', array($this, 'enhance_excerpt_display'));
        
        // إضافة أنماط CSS للمحتوى
        add_action('wp_head', array($this, 'add_content_styles'));
        
        // إصلاح طول المحتوى
        add_filter('excerpt_length', array($this, 'custom_excerpt_length'));
        add_filter('excerpt_more', array($this, 'custom_excerpt_more'));
        
        // تحسين عرض المقالات في التصنيفات
        add_action('pre_get_posts', array($this, 'modify_category_query'));
        
        // إضافة shortcode لعرض المحتوى الكامل
        add_shortcode('nahda_full_content', array($this, 'display_full_content'));
        add_shortcode('nahda_formatted_post', array($this, 'display_formatted_post'));
    }
    
    /**
     * تحسين عرض المحتوى
     */
    public function enhance_content_display($content) {
        // إذا كان في صفحة مقال واحد، عرض المحتوى كاملاً
        if (is_single()) {
            return $this->format_full_content($content);
        }
        
        // إذا كان في صفحة تصنيف، عرض محتوى أطول
        if (is_category() || is_archive()) {
            return $this->format_archive_content($content);
        }
        
        return $content;
    }
    
    /**
     * تنسيق المحتوى الكامل
     */
    private function format_full_content($content) {
        // تنظيف وتنسيق المحتوى
        $content = $this->clean_and_format_text($content);
        
        // إضافة فقرات منسقة
        $content = wpautop($content);
        
        // إضافة تنسيق خاص للنصوص العربية
        $content = $this->add_arabic_formatting($content);
        
        // إضافة جدول المحتويات إذا كان النص طويل
        if (str_word_count(strip_tags($content)) > 500) {
            $toc = $this->generate_table_of_contents($content);
            $content = $toc . $content;
        }
        
        return '<div class="nahda-full-content">' . $content . '</div>';
    }
    
    /**
     * تنسيق محتوى الأرشيف
     */
    private function format_archive_content($content) {
        // عرض أول 500 كلمة بدلاً من 3 أسطر
        $words = explode(' ', strip_tags($content));
        if (count($words) > 500) {
            $content = implode(' ', array_slice($words, 0, 500)) . '...';
        }
        
        // تنسيق النص
        $content = $this->clean_and_format_text($content);
        $content = wpautop($content);
        
        // إضافة رابط "قراءة المزيد"
        $content .= '<div class="read-more-wrapper">';
        $content .= '<a href="' . get_permalink() . '" class="nahda-read-more">📖 قراءة المقال كاملاً</a>';
        $content .= '</div>';
        
        return '<div class="nahda-archive-content">' . $content . '</div>';
    }
    
    /**
     * تنظيف وتنسيق النص
     */
    private function clean_and_format_text($content) {
        // إزالة المسافات الزائدة
        $content = preg_replace('/\s+/', ' ', $content);
        
        // تنسيق علامات الترقيم العربية
        $content = str_replace(array('،', '؛', '؟', '!'), array('، ', '؛ ', '؟ ', '! '), $content);
        
        // تنسيق الأرقام العربية
        $content = $this->format_arabic_numbers($content);
        
        // تنسيق العناوين الفرعية
        $content = $this->format_subheadings($content);
        
        return trim($content);
    }
    
    /**
     * إضافة تنسيق عربي خاص
     */
    private function add_arabic_formatting($content) {
        // تمييز الكلمات المهمة
        $important_words = array(
            'النهضة', 'المشروع', 'الدولة', 'السورية', 'التنمية', 'الإصلاح',
            'الديمقراطية', 'الحرية', 'العدالة', 'التطوير', 'البناء'
        );
        
        foreach ($important_words as $word) {
            $content = str_replace($word, '<span class="important-word">' . $word . '</span>', $content);
        }
        
        // تمييز الأرقام والإحصائيات
        $content = preg_replace('/(\d+%|\d+\.\d+%|\d+,\d+)/', '<span class="statistic">$1</span>', $content);
        
        // تمييز التواريخ
        $content = preg_replace('/(\d{4}م|\d{4}هـ|\d{1,2}\/\d{1,2}\/\d{4})/', '<span class="date">$1</span>', $content);
        
        return $content;
    }
    
    /**
     * تنسيق الأرقام العربية
     */
    private function format_arabic_numbers($content) {
        // تحويل الأرقام الإنجليزية للعربية إذا لزم الأمر
        $english_numbers = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
        $arabic_numbers = array('٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩');
        
        // يمكن تفعيل هذا حسب الحاجة
        // $content = str_replace($english_numbers, $arabic_numbers, $content);
        
        return $content;
    }
    
    /**
     * تنسيق العناوين الفرعية
     */
    private function format_subheadings($content) {
        // تحديد العناوين الفرعية وتنسيقها
        $patterns = array(
            '/^(أولاً[:：].*?)$/m' => '<h3 class="sub-heading level-1">$1</h3>',
            '/^(ثانياً[:：].*?)$/m' => '<h3 class="sub-heading level-1">$1</h3>',
            '/^(ثالثاً[:：].*?)$/m' => '<h3 class="sub-heading level-1">$1</h3>',
            '/^(رابعاً[:：].*?)$/m' => '<h3 class="sub-heading level-1">$1</h3>',
            '/^(خامساً[:：].*?)$/m' => '<h3 class="sub-heading level-1">$1</h3>',
            '/^(\d+\.\s.*?)$/m' => '<h4 class="sub-heading level-2">$1</h4>',
            '/^([أ-ي]\.\s.*?)$/m' => '<h5 class="sub-heading level-3">$1</h5>',
        );
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }
        
        return $content;
    }
    
    /**
     * إنشاء جدول المحتويات
     */
    private function generate_table_of_contents($content) {
        // استخراج العناوين
        preg_match_all('/<h[3-5][^>]*>(.*?)<\/h[3-5]>/i', $content, $matches);
        
        if (empty($matches[1])) {
            return '';
        }
        
        $toc = '<div class="nahda-table-of-contents">';
        $toc .= '<h3>📋 محتويات المقال</h3>';
        $toc .= '<ul class="toc-list">';
        
        foreach ($matches[1] as $index => $heading) {
            $anchor = 'heading-' . ($index + 1);
            $clean_heading = strip_tags($heading);
            $toc .= '<li><a href="#' . $anchor . '">' . $clean_heading . '</a></li>';
            
            // إضافة الرابط للعنوان في المحتوى
            $content = str_replace($matches[0][$index], 
                str_replace('>', ' id="' . $anchor . '">', $matches[0][$index]), $content);
        }
        
        $toc .= '</ul></div>';
        
        return $toc;
    }
    
    /**
     * تحسين طول المقتطف
     */
    public function custom_excerpt_length($length) {
        // زيادة طول المقتطف إلى 100 كلمة
        return 100;
    }
    
    /**
     * تخصيص نص "قراءة المزيد"
     */
    public function custom_excerpt_more($more) {
        return '... <a href="' . get_permalink() . '" class="read-more-link">📖 قراءة المزيد</a>';
    }
    
    /**
     * تعديل استعلام التصنيفات
     */
    public function modify_category_query($query) {
        if (!is_admin() && $query->is_main_query()) {
            if (is_category()) {
                // عرض 20 مقال في صفحة التصنيف
                $query->set('posts_per_page', 20);
                
                // ترتيب حسب التاريخ
                $query->set('orderby', 'date');
                $query->set('order', 'ASC');
            }
        }
    }
    
    /**
     * عرض المحتوى الكامل - Shortcode
     */
    public function display_full_content($atts) {
        $atts = shortcode_atts(array(
            'post_id' => get_the_ID(),
            'show_title' => 'true',
            'show_meta' => 'true'
        ), $atts);
        
        $post = get_post($atts['post_id']);
        if (!$post) {
            return '<p>المقال غير موجود.</p>';
        }
        
        ob_start();
        
        echo '<article class="nahda-full-article">';
        
        if ($atts['show_title'] === 'true') {
            echo '<header class="article-header">';
            echo '<h1 class="article-title">' . get_the_title($post->ID) . '</h1>';
            
            if ($atts['show_meta'] === 'true') {
                echo '<div class="article-meta">';
                echo '<span class="publish-date">📅 ' . get_the_date('Y/m/d', $post->ID) . '</span>';
                
                $categories = get_the_category($post->ID);
                if (!empty($categories)) {
                    echo '<span class="article-category">📂 ';
                    foreach ($categories as $category) {
                        echo '<a href="' . get_category_link($category->term_id) . '">' . $category->name . '</a> ';
                    }
                    echo '</span>';
                }
                echo '</div>';
            }
            echo '</header>';
        }
        
        echo '<div class="article-content">';
        $content = apply_filters('the_content', $post->post_content);
        echo $this->format_full_content($content);
        echo '</div>';
        
        echo '</article>';
        
        return ob_get_clean();
    }
    
    /**
     * عرض مقال منسق - Shortcode
     */
    public function display_formatted_post($atts) {
        $atts = shortcode_atts(array(
            'category' => '',
            'limit' => 10,
            'show_full' => 'false'
        ), $atts);
        
        $args = array(
            'post_type' => 'post',
            'posts_per_page' => intval($atts['limit']),
            'post_status' => 'publish'
        );
        
        if (!empty($atts['category'])) {
            $args['category_name'] = $atts['category'];
        }
        
        $posts = get_posts($args);
        
        if (empty($posts)) {
            return '<p>لا توجد مقالات.</p>';
        }
        
        ob_start();
        
        echo '<div class="nahda-formatted-posts">';
        
        foreach ($posts as $post) {
            setup_postdata($post);
            
            echo '<article class="formatted-post-item">';
            echo '<h3 class="post-title"><a href="' . get_permalink($post->ID) . '">' . get_the_title($post->ID) . '</a></h3>';
            
            if ($atts['show_full'] === 'true') {
                $content = apply_filters('the_content', $post->post_content);
                echo '<div class="post-content">' . $this->format_full_content($content) . '</div>';
            } else {
                $content = get_the_excerpt($post->ID);
                echo '<div class="post-excerpt">' . $this->format_archive_content($content) . '</div>';
            }
            
            echo '</article>';
        }
        
        echo '</div>';
        
        wp_reset_postdata();
        
        return ob_get_clean();
    }
    
    /**
     * إضافة أنماط CSS للمحتوى
     */
    public function add_content_styles() {
        ?>
        <style type="text/css">
        /* تنسيق المحتوى الكامل */
        .nahda-full-content {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.8;
            color: #2c3e50;
            direction: rtl;
            text-align: right;
        }
        
        .nahda-full-content p {
            margin-bottom: 1.5em;
            font-size: 16px;
            text-align: justify;
        }
        
        .nahda-archive-content {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #34495e;
            direction: rtl;
            text-align: right;
        }
        
        /* الكلمات المهمة */
        .important-word {
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }
        
        /* الإحصائيات */
        .statistic {
            background: #3498db;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        /* التواريخ */
        .date {
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        /* العناوين الفرعية */
        .sub-heading {
            margin: 2em 0 1em 0;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .sub-heading.level-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.3em;
        }
        
        .sub-heading.level-2 {
            background: #f8f9fa;
            border-right: 4px solid #3498db;
            color: #2c3e50;
            font-size: 1.2em;
        }
        
        .sub-heading.level-3 {
            background: #fff3cd;
            border-right: 3px solid #ffc107;
            color: #856404;
            font-size: 1.1em;
        }
        
        /* جدول المحتويات */
        .nahda-table-of-contents {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 2em;
        }
        
        .nahda-table-of-contents h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        
        .toc-list {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
        }
        
        .toc-list li {
            margin-bottom: 8px;
            padding-right: 20px;
            position: relative;
        }
        
        .toc-list li:before {
            content: "📄";
            position: absolute;
            right: 0;
            top: 0;
        }
        
        .toc-list a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }
        
        .toc-list a:hover {
            color: #2980b9;
            text-decoration: underline;
        }
        
        /* رابط قراءة المزيد */
        .read-more-wrapper {
            text-align: center;
            margin-top: 2em;
            padding-top: 1em;
            border-top: 1px solid #dee2e6;
        }
        
        .nahda-read-more, .read-more-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .nahda-read-more:hover, .read-more-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        /* تنسيق المقال الكامل */
        .nahda-full-article {
            max-width: 800px;
            margin: 0 auto;
            padding: 2em;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .article-header {
            border-bottom: 3px solid #3498db;
            padding-bottom: 1em;
            margin-bottom: 2em;
        }
        
        .article-title {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 0.5em;
            line-height: 1.3;
        }
        
        .article-meta {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .article-meta span {
            margin-left: 15px;
        }
        
        .article-meta a {
            color: #3498db;
            text-decoration: none;
        }
        
        .article-meta a:hover {
            text-decoration: underline;
        }
        
        /* المقالات المنسقة */
        .nahda-formatted-posts {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .formatted-post-item {
            background: white;
            border-radius: 8px;
            padding: 2em;
            margin-bottom: 2em;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-right: 4px solid #3498db;
        }
        
        .formatted-post-item .post-title {
            margin-top: 0;
            margin-bottom: 1em;
        }
        
        .formatted-post-item .post-title a {
            color: #2c3e50;
            text-decoration: none;
        }
        
        .formatted-post-item .post-title a:hover {
            color: #3498db;
        }
        
        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            .nahda-full-content, .nahda-archive-content {
                font-size: 14px;
                line-height: 1.6;
            }
            
            .nahda-full-article {
                padding: 1em;
                margin: 0 10px;
            }
            
            .article-title {
                font-size: 1.8em;
            }
            
            .sub-heading {
                font-size: 1.1em !important;
                padding: 8px 12px;
            }
        }
        </style>
        <?php
    }
}

// تفعيل الكلاس
new NahdaContentFixer();

// دوال مساعدة
function nahda_display_full_post($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    echo do_shortcode('[nahda_full_content post_id="' . $post_id . '"]');
}

function nahda_display_category_posts($category, $limit = 10, $show_full = false) {
    $show_full_attr = $show_full ? 'true' : 'false';
    echo do_shortcode('[nahda_formatted_post category="' . $category . '" limit="' . $limit . '" show_full="' . $show_full_attr . '"]');
}
?>
