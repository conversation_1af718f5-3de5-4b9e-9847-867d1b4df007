#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد ملفات XML للاستيراد في WordPress
إنشاء ملفات XML محسنة ومقسمة للاستيراد السهل
"""

import json
import os
from datetime import datetime

class WordPressXMLGenerator:
    """مولد ملفات XML لـ WordPress"""
    
    def __init__(self):
        self.wp_structure = None
        self.xml_files = []
        
    def load_structure(self):
        """تحميل بنية WordPress"""
        try:
            with open('wordpress_structure_ready.json', 'r', encoding='utf-8') as f:
                self.wp_structure = json.load(f)
            
            print(f"✅ تم تحميل البنية:")
            print(f"   📂 التصنيفات: {len(self.wp_structure['categories'])}")
            print(f"   📄 المقالات: {len(self.wp_structure['posts'])}")
            return True
            
        except FileNotFoundError:
            print("❌ لم يتم العثور على ملف البنية. يرجى تشغيل create_import_files.py أولاً")
            return False
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False
    
    def create_xml_header(self):
        """إنشاء رأس ملف XML"""
        return '''<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0"
    xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:wfw="http://wellformedweb.org/CommentAPI/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:wp="http://wordpress.org/export/1.2/">

<channel>
    <title>مشروع النهضة السورية</title>
    <link>http://localhost</link>
    <description>كتاب مشروع النهضة وبناء الدولة السورية</description>
    <pubDate>{}</pubDate>
    <language>ar</language>
    <wp:wxr_version>1.2</wp:wxr_version>
    <wp:base_site_url>http://localhost</wp:base_site_url>
    <wp:base_blog_url>http://localhost</wp:base_blog_url>

    <wp:author>
        <wp:author_id>1</wp:author_id>
        <wp:author_login>admin</wp:author_login>
        <wp:author_email><EMAIL></wp:author_email>
        <wp:author_display_name><![CDATA[مؤلف النهضة]]></wp:author_display_name>
        <wp:author_first_name><![CDATA[]]></wp:author_first_name>
        <wp:author_last_name><![CDATA[]]></wp:author_last_name>
    </wp:author>
'''.format(datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000'))
    
    def create_xml_footer(self):
        """إنشاء تذييل ملف XML"""
        return '''
</channel>
</rss>'''
    
    def escape_xml(self, text):
        """تنظيف النص لـ XML"""
        if not text:
            return ''
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#39;'))
    
    def create_category_xml(self, category):
        """إنشاء XML للتصنيف"""
        cat_id = category['id']
        name = self.escape_xml(category['name'])
        slug = category['slug']
        
        # تحديد الأب
        parent_slug = ''
        if category['parent'] > 0:
            parent_cat = next((cat for cat in self.wp_structure['categories'] 
                             if cat['id'] == category['parent']), None)
            if parent_cat:
                parent_slug = parent_cat['slug']
        
        # وصف حسب النوع
        descriptions = {
            'part': 'قسم رئيسي من مشروع النهضة',
            'chapter': 'باب من أبواب المشروع',
            'section': 'فصل تفصيلي',
            'subsection': 'مبحث أو محور فرعي'
        }
        description = descriptions.get(category.get('type', ''), 'تصنيف من كتاب النهضة')
        
        return f'''
    <wp:category>
        <wp:term_id>{cat_id}</wp:term_id>
        <wp:category_nicename>{slug}</wp:category_nicename>
        <wp:category_parent>{parent_slug}</wp:category_parent>
        <wp:cat_name><![CDATA[{name}]]></wp:cat_name>
        <wp:category_description><![CDATA[{description}]]></wp:category_description>
    </wp:category>'''
    
    def create_post_xml(self, post, post_id):
        """إنشاء XML للمقال"""
        title = self.escape_xml(post['title'][:200])
        content = post['content'][:3000]  # محتوى أطول
        excerpt = post.get('excerpt', '')[:400]
        slug = post['slug'][:60]
        
        # العثور على التصنيف
        category_slug = 'uncategorized'
        category_name = 'غير مصنف'
        
        if post.get('category_id'):
            category = next((cat for cat in self.wp_structure['categories'] 
                           if cat['id'] == post['category_id']), None)
            if category:
                category_slug = category['slug']
                category_name = category['name']
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return f'''
    <item>
        <title><![CDATA[{title}]]></title>
        <link>http://localhost/?p={post_id}</link>
        <pubDate>{datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}</pubDate>
        <dc:creator><![CDATA[admin]]></dc:creator>
        <guid isPermaLink="false">http://localhost/?p={post_id}</guid>
        <description></description>
        <content:encoded><![CDATA[{content}]]></content:encoded>
        <excerpt:encoded><![CDATA[{excerpt}]]></excerpt:encoded>
        <wp:post_id>{post_id}</wp:post_id>
        <wp:post_date><![CDATA[{current_time}]]></wp:post_date>
        <wp:post_date_gmt><![CDATA[{current_time}]]></wp:post_date_gmt>
        <wp:comment_status><![CDATA[open]]></wp:comment_status>
        <wp:ping_status><![CDATA[open]]></wp:ping_status>
        <wp:post_name><![CDATA[{slug}]]></wp:post_name>
        <wp:status><![CDATA[publish]]></wp:status>
        <wp:post_parent>0</wp:post_parent>
        <wp:menu_order>0</wp:menu_order>
        <wp:post_type><![CDATA[post]]></wp:post_type>
        <wp:post_password><![CDATA[]]></wp:post_password>
        <wp:is_sticky>0</wp:is_sticky>
        <category domain="category" nicename="{category_slug}"><![CDATA[{category_name}]]></category>
    </item>'''
    
    def create_test_file(self):
        """إنشاء ملف اختبار"""
        print("🧪 إنشاء ملف الاختبار...")
        
        content = self.create_xml_header()
        
        # إضافة عينة من التصنيفات (20 تصنيف)
        sample_categories = self.wp_structure['categories'][:20]
        for category in sample_categories:
            content += self.create_category_xml(category)
        
        # إضافة عينة من المقالات (15 مقال)
        sample_posts = self.wp_structure['posts'][:15]
        for i, post in enumerate(sample_posts):
            content += self.create_post_xml(post, i + 1)
        
        content += self.create_xml_footer()
        
        filename = 'test_hierarchical.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({size:.1f} KB)")
        print(f"   📊 المحتوى: {len(sample_categories)} تصنيف + {len(sample_posts)} مقال")
        return filename
    
    def create_categories_file(self):
        """إنشاء ملف التصنيفات الكامل"""
        print("📂 إنشاء ملف التصنيفات...")
        
        content = self.create_xml_header()
        
        # ترتيب التصنيفات حسب المستوى
        sorted_categories = sorted(self.wp_structure['categories'], 
                                 key=lambda x: (x.get('level', 1), x.get('id', 0)))
        
        for category in sorted_categories:
            content += self.create_category_xml(category)
        
        content += self.create_xml_footer()
        
        filename = 'hierarchical_categories.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.xml_files.append(filename)
        size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({size:.1f} KB - {len(sorted_categories)} تصنيف)")
        return filename
    
    def create_posts_files(self, num_files=9):
        """إنشاء ملفات المقالات"""
        print(f"📄 إنشاء {num_files} ملف للمقالات...")
        
        posts = self.wp_structure['posts']
        posts_per_file = len(posts) // num_files + 1
        
        for i in range(num_files):
            start_idx = i * posts_per_file
            end_idx = min((i + 1) * posts_per_file, len(posts))
            chunk = posts[start_idx:end_idx]
            
            if not chunk:
                break
            
            content = self.create_xml_header()
            
            for j, post in enumerate(chunk):
                post_id = start_idx + j + 1
                content += self.create_post_xml(post, post_id)
            
            content += self.create_xml_footer()
            
            filename = f'posts_part_{i+1:02d}.xml'
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.xml_files.append(filename)
            size = os.path.getsize(filename) / 1024
            print(f"✅ تم إنشاء: {filename} ({size:.1f} KB - {len(chunk)} مقال)")
        
        return len([f for f in self.xml_files if f.startswith('posts_part_')])
    
    def create_import_guide(self, posts_files_count):
        """إنشاء دليل الاستيراد"""
        guide = f"""# 🚀 دليل استيراد كتاب النهضة

## 📁 الملفات الجاهزة

تم إنشاء ملفات XML محسنة للاستيراد:

### 🧪 **ملف الاختبار:**
- `test_hierarchical.xml` - اختبار البنية الهرمية

### 📂 **ملف التصنيفات:**
- `hierarchical_categories.xml` - جميع التصنيفات الهرمية

### 📄 **ملفات المقالات:**
- `posts_part_01.xml` إلى `posts_part_{posts_files_count:02d}.xml` - المقالات مقسمة

## 🚀 خطوات الاستيراد

### 1️⃣ **اختبار البنية (5 دقائق):**
```
أدوات → استيراد → WordPress → test_hierarchical.xml
```

### 2️⃣ **استيراد التصنيفات (10 دقائق):**
```
أدوات → استيراد → WordPress → hierarchical_categories.xml
```

### 3️⃣ **استيراد المقالات ({posts_files_count * 5}-{posts_files_count * 10} دقيقة):**
```
أدوات → استيراد → WordPress → posts_part_01.xml
أدوات → استيراد → WordPress → posts_part_02.xml
... (استمر بالترتيب حتى posts_part_{posts_files_count:02d}.xml)
```

## ✅ التحقق من النجاح

### في لوحة التحكم:
1. **المقالات** → **التصنيفات**
2. يجب أن تجد البنية الهرمية:
   ```
   📚 القسم الأول
   ├── 📖 الباب الأول
   │   ├── 📝 الفصل الأول
   │   │   └── 🔸 المبحث الأول
   │   └── 📝 الفصل الثاني
   └── 📖 الباب الثاني
   ```

## 📊 الإحصائيات

| العنصر | العدد | الوقت المتوقع |
|---------|-------|---------------|
| 📂 التصنيفات | {len(self.wp_structure['categories'])} | 10 دقائق |
| 📄 المقالات | {len(self.wp_structure['posts'])} | {posts_files_count * 5}-{posts_files_count * 10} دقائق |
| **المجموع** | **{len(self.wp_structure['categories']) + len(self.wp_structure['posts'])}** | **{15 + posts_files_count * 5}-{20 + posts_files_count * 10} دقائق** |

## ⚠️ تعليمات مهمة

1. **استورد الملفات بالترتيب المحدد**
2. **انتظر اكتمال كل ملف** قبل الانتقال للتالي
3. **تحقق من النتائج** بعد كل مجموعة
4. **احتفظ بنسخة احتياطية** قبل البدء

---

**🎉 بعد الاستيراد ستحصل على موقع WordPress شامل لكتاب النهضة!**
"""
        
        with open('IMPORT_GUIDE.md', 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print("✅ تم إنشاء: IMPORT_GUIDE.md")

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء ملفات XML للاستيراد")
    print("=" * 50)
    
    generator = WordPressXMLGenerator()
    
    # تحميل البنية
    if not generator.load_structure():
        return False
    
    print(f"\n🔧 إنشاء ملفات XML...")
    
    # إنشاء ملف الاختبار
    generator.create_test_file()
    
    # إنشاء ملف التصنيفات
    generator.create_categories_file()
    
    # إنشاء ملفات المقالات
    posts_files_count = generator.create_posts_files(9)
    
    # إنشاء دليل الاستيراد
    generator.create_import_guide(posts_files_count)
    
    # الملخص النهائي
    print(f"\n" + "=" * 50)
    print("🎉 تم إنشاء ملفات XML بنجاح!")
    
    all_files = ['test_hierarchical.xml', 'hierarchical_categories.xml'] + generator.xml_files
    total_size = 0
    
    print(f"\n📁 الملفات المُنشأة:")
    for xml_file in all_files:
        if os.path.exists(xml_file):
            size = os.path.getsize(xml_file) / 1024
            total_size += size
            print(f"   - {xml_file} ({size:.1f} KB)")
    
    print(f"\n📊 الإحصائيات النهائية:")
    print(f"   📁 إجمالي الملفات: {len(all_files)}")
    print(f"   💾 إجمالي الحجم: {total_size:.1f} KB ({total_size/1024:.1f} MB)")
    print(f"   📂 التصنيفات: {len(generator.wp_structure['categories'])}")
    print(f"   📄 المقالات: {len(generator.wp_structure['posts'])}")
    
    print(f"\n🎯 الخطوات التالية:")
    print(f"   1. اقرأ IMPORT_GUIDE.md للتعليمات")
    print(f"   2. ابدأ بـ test_hierarchical.xml للاختبار")
    print(f"   3. استورد hierarchical_categories.xml للتصنيفات")
    print(f"   4. استورد ملفات posts_part بالترتيب")
    
    return True

if __name__ == "__main__":
    main()
