#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WordPress XML Fixer and Splitter
Fixes WXR format issues and splits large files into smaller chunks
"""

import json
import xml.etree.ElementTree as ET
from datetime import datetime
import html
import os

class WordPressXMLFixer:
    """Fix and optimize WordPress XML files"""

    def __init__(self, structure_file='book_structure.json'):
        self.structure = None
        self.load_structure(structure_file)

    def load_structure(self, file_path):
        """Load structure from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.structure = json.load(f)
            print(f"✅ تم تحميل البنية من: {file_path}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False

    def create_valid_wxr_header(self):
        """Create a valid WXR XML header"""
        # Create root RSS element with all required namespaces (fixed)
        rss = ET.Element('rss')
        rss.set('version', '2.0')
        rss.set('xmlns:excerpt', 'http://wordpress.org/export/1.2/excerpt/')
        rss.set('xmlns:content', 'http://purl.org/rss/1.0/modules/content/')
        rss.set('xmlns:wfw', 'http://wellformedweb.org/CommentAPI/')
        rss.set('xmlns:dc', 'http://purl.org/dc/elements/1.1/')
        rss.set('xmlns:wp', 'http://wordpress.org/export/1.2/')

        channel = ET.SubElement(rss, 'channel')

        # Add required channel metadata
        ET.SubElement(channel, 'title').text = 'مشروع النهضة وبناء الدولة السورية'
        ET.SubElement(channel, 'link').text = 'http://localhost'
        ET.SubElement(channel, 'description').text = 'كتاب مشروع النهضة وبناء الدولة السورية - ما بعد الاستبداد'
        ET.SubElement(channel, 'pubDate').text = datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')
        ET.SubElement(channel, 'language').text = 'ar'
        ET.SubElement(channel, 'generator').text = 'https://wordpress.org/?v=6.4'

        # Critical WXR elements
        wxr_version = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}wxr_version')
        wxr_version.text = '1.2'

        base_site_url = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}base_site_url')
        base_site_url.text = 'http://localhost'

        base_blog_url = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}base_blog_url')
        base_blog_url.text = 'http://localhost'

        return rss, channel

    def create_categories_xml(self):
        """Create categories-only XML file"""
        print("📁 إنشاء ملف التصنيفات...")

        rss, channel = self.create_valid_wxr_header()

        # Add only categories
        category_id = 1
        for part in self.structure['parts'][:20]:  # First 20 parts only
            cat_elem = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}category')
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}term_id').text = str(category_id)
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_nicename').text = f"part-{part['id']}"
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_parent').text = ''
            ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}cat_name').text = part['title'][:100]  # Limit title length
            category_id += 1

        # Add chapters as sub-categories (limited)
        for chapter in self.structure['chapters'][:50]:  # First 50 chapters only
            if chapter.get('part_id') and chapter['part_id'] <= 20:  # Only for first 20 parts
                cat_elem = ET.SubElement(channel, '{http://wordpress.org/export/1.2/}category')
                ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}term_id').text = str(category_id)
                ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_nicename').text = f"chapter-{chapter['id']}"
                ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}category_parent').text = f"part-{chapter['part_id']}"
                ET.SubElement(cat_elem, '{http://wordpress.org/export/1.2/}cat_name').text = chapter['title'][:100]
                category_id += 1

        return self.save_xml(rss, 'wordpress_categories.xml')

    def create_posts_xml(self, start_index=0, max_posts=100):
        """Create posts-only XML file with limited number of posts"""
        print(f"📝 إنشاء ملف المقالات ({start_index} إلى {start_index + max_posts})...")

        rss, channel = self.create_valid_wxr_header()

        # Add limited posts
        post_id = start_index + 1
        content_slice = self.structure['content'][start_index:start_index + max_posts]

        for content_unit in content_slice:
            item = ET.SubElement(channel, 'item')

            # Basic post data
            title = content_unit['title'][:100] if len(content_unit['title']) > 100 else content_unit['title']
            ET.SubElement(item, 'title').text = title
            ET.SubElement(item, 'link').text = f"http://localhost/?p={post_id}"
            ET.SubElement(item, 'pubDate').text = datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')
            ET.SubElement(item, '{http://purl.org/dc/elements/1.1/}creator').text = 'admin'
            ET.SubElement(item, 'guid', {'isPermaLink': 'false'}).text = f"http://localhost/?p={post_id}"
            ET.SubElement(item, 'description').text = ''

            # Post content (limited)
            content_text = self._build_limited_post_content(content_unit)
            content_elem = ET.SubElement(item, '{http://purl.org/rss/1.0/modules/content/}encoded')
            content_elem.text = content_text

            # WordPress specific data
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_id').text = str(post_id)
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_date').text = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_date_gmt').text = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}comment_status').text = 'open'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}ping_status').text = 'open'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_name').text = f"post-{post_id}"
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}status').text = 'publish'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_parent').text = '0'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}menu_order').text = '0'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_type').text = 'post'
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}post_password').text = ''
            ET.SubElement(item, '{http://wordpress.org/export/1.2/}is_sticky').text = '0'

            # Add category if exists
            if content_unit.get('chapter_id') and content_unit['chapter_id'] <= 50:
                chapter = next((c for c in self.structure['chapters'] if c['id'] == content_unit['chapter_id']), None)
                if chapter:
                    category = ET.SubElement(item, 'category', {
                        'domain': 'category',
                        'nicename': f"chapter-{chapter['id']}"
                    })
                    category.text = chapter['title'][:50]

            post_id += 1

        filename = f'wordpress_posts_{start_index + 1}_to_{start_index + len(content_slice)}.xml'
        return self.save_xml(rss, filename)

    def _build_limited_post_content(self, content_unit):
        """Build limited HTML content for post"""
        html_content = []

        # Limit content to prevent oversized posts
        content_items = content_unit.get('content', [])[:5]  # Max 5 content items per post

        for item in content_items:
            if item['type'] == 'content':
                text = item['text'][:1000]  # Limit text length
                html_content.append(f"<p>{html.escape(text)}</p>")
            elif item['type'] == 'summary':
                text = item['text'][:500]  # Shorter for summaries
                html_content.append(f"<div class='chapter-summary'><h3>خلاصة</h3><p>{html.escape(text)}</p></div>")

        return '\n'.join(html_content)

    def save_xml(self, root, filename):
        """Save XML to file with proper formatting"""
        try:
            # Add indentation
            self._indent(root)
            tree = ET.ElementTree(root)

            with open(filename, 'wb') as f:
                tree.write(f, encoding='utf-8', xml_declaration=True)

            file_size = os.path.getsize(filename) / (1024 * 1024)  # Size in MB
            print(f"✅ تم حفظ {filename} ({file_size:.2f} MB)")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ {filename}: {e}")
            return False

    def _indent(self, elem, level=0):
        """Add indentation to XML for readability"""
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                self._indent(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i

    def create_split_files(self):
        """Create multiple smaller XML files"""
        print("🔧 إنشاء ملفات XML مقسمة...")

        # Create categories file
        self.create_categories_xml()

        # Create multiple post files
        total_content = len(self.structure['content'])
        posts_per_file = 50  # Smaller chunks

        for start in range(0, min(total_content, 200), posts_per_file):  # Limit to first 200 posts
            self.create_posts_xml(start, posts_per_file)

        print(f"✅ تم إنشاء ملفات مقسمة للاستيراد")

def main():
    """Main function"""
    print("🔧 إصلاح وتقسيم ملفات WordPress XML")
    print("=" * 50)

    fixer = WordPressXMLFixer()
    if fixer.structure:
        fixer.create_split_files()

        print("\n📋 تعليمات الاستيراد:")
        print("1. ابدأ بملف wordpress_categories.xml لاستيراد التصنيفات")
        print("2. ثم استورد ملفات المقالات واحد تلو الآخر")
        print("3. كل ملف أصغر من 2 MB ويجب أن يعمل بدون مشاكل")
    else:
        print("❌ لم يتم العثور على ملف البنية")

if __name__ == "__main__":
    main()
