#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WordPress Helper Tools
Additional utilities for WordPress import and validation
"""

import json
import re
from collections import defaultdict

class WordPressValidator:
    """Validate WordPress import structure"""
    
    def __init__(self, structure_file='book_structure.json'):
        self.structure = None
        self.load_structure(structure_file)
    
    def load_structure(self, file_path):
        """Load structure from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.structure = json.load(f)
            print(f"✅ تم تحميل البنية من: {file_path}")
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False
    
    def validate_hierarchy(self):
        """Validate hierarchical structure"""
        if not self.structure:
            return False
        
        print("🔍 جاري التحقق من صحة البنية الهرمية...")
        
        issues = []
        
        # Check parts
        parts = self.structure.get('parts', [])
        if not parts:
            issues.append("⚠️ لم يتم العثور على أي أبواب")
        
        # Check chapters
        chapters = self.structure.get('chapters', [])
        orphaned_chapters = [c for c in chapters if not c.get('part_id')]
        if orphaned_chapters:
            issues.append(f"⚠️ {len(orphaned_chapters)} فصل بدون باب رئيسي")
        
        # Check content units
        content_units = self.structure.get('content', [])
        orphaned_content = [c for c in content_units if not c.get('chapter_id')]
        if orphaned_content:
            issues.append(f"⚠️ {len(orphaned_content)} وحدة محتوى بدون فصل")
        
        if issues:
            print("❌ تم العثور على مشاكل في البنية:")
            for issue in issues:
                print(f"   {issue}")
            return False
        else:
            print("✅ البنية الهرمية صحيحة")
            return True
    
    def generate_statistics(self):
        """Generate content statistics"""
        if not self.structure:
            return
        
        print("\n📊 إحصائيات المحتوى:")
        print("=" * 30)
        
        parts = self.structure.get('parts', [])
        chapters = self.structure.get('chapters', [])
        sections = self.structure.get('sections', [])
        content_units = self.structure.get('content', [])
        
        print(f"📚 إجمالي الأبواب: {len(parts)}")
        print(f"📖 إجمالي الفصول: {len(chapters)}")
        print(f"📝 إجمالي المباحث: {len(sections)}")
        print(f"📄 إجمالي وحدات المحتوى: {len(content_units)}")
        
        # Content per chapter
        chapter_content = defaultdict(int)
        for content in content_units:
            if content.get('chapter_id'):
                chapter_content[content['chapter_id']] += 1
        
        if chapter_content:
            avg_content = sum(chapter_content.values()) / len(chapter_content)
            print(f"📈 متوسط المحتوى لكل فصل: {avg_content:.1f} وحدة")
        
        # Word count estimation
        total_words = 0
        for content in content_units:
            for item in content.get('content', []):
                if item.get('text'):
                    words = len(item['text'].split())
                    total_words += words
        
        print(f"📝 تقدير إجمالي الكلمات: {total_words:,}")
        
        return {
            'parts': len(parts),
            'chapters': len(chapters),
            'sections': len(sections),
            'content_units': len(content_units),
            'estimated_words': total_words
        }

class CategoryTreeGenerator:
    """Generate category tree visualization"""
    
    def __init__(self, structure):
        self.structure = structure
    
    def generate_tree(self):
        """Generate hierarchical tree view"""
        print("\n🌳 شجرة التصنيفات:")
        print("=" * 40)
        
        parts = self.structure.get('parts', [])
        chapters = self.structure.get('chapters', [])
        
        for part in parts:
            print(f"📚 {part['title']}")
            
            # Find chapters for this part
            part_chapters = [c for c in chapters if c.get('part_id') == part['id']]
            
            for i, chapter in enumerate(part_chapters):
                is_last_chapter = (i == len(part_chapters) - 1)
                prefix = "└── " if is_last_chapter else "├── "
                print(f"   {prefix}📖 {chapter['title']}")
                
                # Count content for this chapter
                content_count = len([c for c in self.structure.get('content', []) 
                                   if c.get('chapter_id') == chapter['id']])
                
                content_prefix = "    " if is_last_chapter else "│   "
                if content_count > 0:
                    print(f"   {content_prefix}    📄 {content_count} وحدة محتوى")
        
        print()

def create_import_instructions():
    """Create detailed import instructions"""
    instructions = """
# تعليمات استيراد المحتوى إلى WordPress

## المتطلبات الأساسية:
1. موقع WordPress يدعم اللغة العربية
2. إضافة WordPress Importer مُفعلة
3. ذاكرة PHP كافية (يُنصح بـ 512MB أو أكثر)
4. وقت تنفيذ PHP مناسب (300 ثانية أو أكثر)

## خطوات الاستيراد:

### 1. تحضير WordPress:
```
- تأكد من تفعيل اللغة العربية في Settings > General
- قم بتثبيت قالب يدعم RTL (Right-to-Left)
- تأكد من تفعيل إضافة WordPress Importer
```

### 2. استيراد المحتوى:
```
1. اذهب إلى Tools > Import
2. اختر "WordPress"
3. ارفع ملف wordpress_import.xml
4. اختر المؤلف أو أنشئ مؤلف جديد
5. تأكد من تحديد "Download and import file attachments"
6. اضغط "Submit"
```

### 3. بعد الاستيراد:
```
- تحقق من التصنيفات في Posts > Categories
- تأكد من ظهور المقالات في Posts > All Posts
- اختبر عرض المحتوى العربي
- تحقق من البنية الهرمية للتصنيفات
```

## استكشاف الأخطاء:

### مشكلة: فشل الاستيراد
```
الحل: زيادة memory_limit و max_execution_time في PHP
```

### مشكلة: النص العربي يظهر كرموز غريبة
```
الحل: تأكد من أن قاعدة البيانات تستخدم utf8mb4_unicode_ci
```

### مشكلة: التصنيفات لا تظهر بشكل هرمي
```
الحل: تحقق من إعدادات القالب ودعمه للتصنيفات الهرمية
```

## تحسينات إضافية:

### 1. تحسين SEO:
```
- استخدم إضافة Yoast SEO أو RankMath
- أضف meta descriptions للمقالات
- تأكد من بنية URLs صديقة للمحركات
```

### 2. تحسين التنقل:
```
- أنشئ قائمة تنقل تعكس بنية الكتاب
- أضف widget للتصنيفات في الشريط الجانبي
- استخدم breadcrumbs للتنقل
```

### 3. تحسين العرض:
```
- استخدم قالب يدعم الطباعة العربية بشكل جيد
- أضف خطوط عربية مناسبة
- تأكد من محاذاة النص من اليمين لليسار
```
"""
    
    with open('import_instructions.md', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("📋 تم إنشاء ملف التعليمات: import_instructions.md")

def main():
    """Main helper function"""
    print("🔧 أدوات مساعدة WordPress")
    print("=" * 30)
    
    # Validate structure if exists
    try:
        validator = WordPressValidator()
        if validator.structure:
            validator.validate_hierarchy()
            stats = validator.generate_statistics()
            
            # Generate category tree
            tree_gen = CategoryTreeGenerator(validator.structure)
            tree_gen.generate_tree()
            
        # Create import instructions
        create_import_instructions()
        
    except FileNotFoundError:
        print("⚠️ لم يتم العثور على ملف book_structure.json")
        print("   قم بتشغيل wordpress_converter.py أولاً")

if __name__ == "__main__":
    main()
