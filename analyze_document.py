#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete WordPress Content Generator for Arabic Book
Converts Nahda.docx to WordPress-compatible format with hierarchical structure
"""

import docx
import re
import json
import xml.etree.ElementTree as ET
from collections import defaultdict, OrderedDict
from datetime import datetime
import html
import uuid

def analyze_document_structure(file_path):
    """Analyze the structure of the Word document"""
    try:
        doc = docx.Document(file_path)
        print(f"Document loaded successfully!")
        print(f"Total paragraphs: {len(doc.paragraphs)}")
        print(f"Total tables: {len(doc.tables)}")

        # Analyze ALL paragraph styles and content
        styles_found = defaultdict(int)
        headings = []
        arabic_keywords = ['الباب', 'الفصل', 'المبحث', 'المطلب', 'الفرع', 'أولاً', 'ثانياً', 'ثالثاً']

        print("\n=== Analyzing document structure (this may take a moment) ===")

        # Sample paragraphs throughout the document
        sample_indices = list(range(0, min(200, len(doc.paragraphs)))) + \
                        list(range(1000, min(1200, len(doc.paragraphs)))) + \
                        list(range(5000, min(5200, len(doc.paragraphs))))

        for i in sample_indices:
            if i >= len(doc.paragraphs):
                break

            para = doc.paragraphs[i]
            if para.text.strip():
                style_name = para.style.name if para.style else 'Normal'
                styles_found[style_name] += 1

                text = para.text.strip()

                # Check if this looks like a heading
                is_heading = False
                heading_level = 0

                # Check style-based headings
                if style_name.startswith('Heading'):
                    is_heading = True
                    heading_level = extract_heading_level(style_name, text)

                # Check content-based headings
                elif any(keyword in text for keyword in arabic_keywords):
                    is_heading = True
                    heading_level = extract_heading_level(style_name, text)

                # Check for short paragraphs that might be headings
                elif len(text) < 150 and not text.endswith('.') and not text.endswith('،'):
                    # Might be a heading
                    if any(keyword in text for keyword in ['الفصل', 'الباب', 'المبحث']):
                        is_heading = True
                        heading_level = extract_heading_level(style_name, text)

                if is_heading:
                    headings.append({
                        'index': i,
                        'style': style_name,
                        'text': text,
                        'level': heading_level
                    })
                    print(f"Heading found at para {i}: Level {heading_level} | {text[:80]}...")

        print(f"\n=== Styles found in document sample ===")
        for style, count in sorted(styles_found.items()):
            print(f"{style}: {count} occurrences")

        print(f"\n=== Hierarchical structure detected ===")
        level_counts = defaultdict(int)
        for heading in headings:
            level_counts[heading['level']] += 1

        for level in sorted(level_counts.keys()):
            print(f"Level {level}: {level_counts[level]} headings")

        return {
            'total_paragraphs': len(doc.paragraphs),
            'total_tables': len(doc.tables),
            'styles': dict(styles_found),
            'headings': headings,
            'level_counts': dict(level_counts)
        }

    except Exception as e:
        print(f"Error analyzing document: {e}")
        return None

def extract_heading_level(style_name, text):
    """Extract heading level from style name or text content"""
    if 'Heading 1' in style_name:
        return 1
    elif 'Heading 2' in style_name:
        return 2
    elif 'Heading 3' in style_name:
        return 3
    elif 'Heading' in style_name:
        # Extract number from heading style
        match = re.search(r'Heading (\d+)', style_name)
        if match:
            return int(match.group(1))

    # Analyze Arabic text patterns
    if 'الباب' in text:
        return 1  # Main section
    elif 'الفصل' in text:
        return 2  # Chapter
    elif 'المبحث' in text:
        return 3  # Sub-chapter
    elif 'المطلب' in text:
        return 4  # Sub-section

    return 0  # Unknown/content

if __name__ == "__main__":
    result = analyze_document_structure('Nahda.docx')
    if result:
        print(f"\n=== Analysis Complete ===")
        print(f"Document contains {result['total_paragraphs']} paragraphs")
        print(f"Found {len(result['headings'])} potential headings")
