#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد المقالات الكاملة المحسن - كتاب النهضة
إنشاء مقالات كاملة مع تحسين استخراج المحتوى لتغطية 99%+ من الكتاب
"""

from docx import Document
import re
import json
import os
from datetime import datetime
from collections import defaultdict

class EnhancedCompleteArticleGenerator:
    """مولد المقالات الكاملة المحسن"""
    
    def __init__(self):
        self.book_structure = {
            'parts': [],      # الأقسام الرئيسية
            'chapters': [],   # الأبواب
            'sections': [],   # الفصول
            'subsections': [], # المباحث
            'articles': []    # المقالات الكاملة
        }
        self.current_article = None
        self.article_content = []
        self.ignored_content = []  # لتتبع المحتوى المتجاهل
        
    def analyze_book_enhanced(self, docx_file):
        """تحليل الكتاب المحسن لاستخراج أقصى محتوى"""
        print("🔍 تحليل الكتاب المحسن لاستخراج المحتوى الكامل...")
        
        try:
            doc = Document(docx_file)
            print(f"✅ تم تحميل الكتاب: {len(doc.paragraphs)} فقرة")
            
            current_part = None
            current_chapter = None
            current_section = None
            current_subsection = None
            
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if not text:
                    continue
                
                # تحديد نوع النص
                text_type = self.classify_text_enhanced(text)
                
                if text_type == 'part':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()
                    
                    current_part = self.create_part(text, len(self.book_structure['parts']) + 1)
                    self.book_structure['parts'].append(current_part)
                    print(f"📚 قسم: {text[:50]}...")
                    
                elif text_type == 'chapter':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()
                    
                    current_chapter = self.create_chapter(text, len(self.book_structure['chapters']) + 1, current_part)
                    self.book_structure['chapters'].append(current_chapter)
                    print(f"📖 باب: {text[:50]}...")
                    
                elif text_type == 'section':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()
                    
                    current_section = self.create_section(text, len(self.book_structure['sections']) + 1, current_chapter)
                    self.book_structure['sections'].append(current_section)
                    print(f"📝 فصل: {text[:50]}...")
                    
                    # بدء مقال جديد للفصل
                    self.start_new_article(text, current_section, current_chapter, current_part)
                    
                elif text_type == 'subsection':
                    # حفظ المقال السابق إن وجد
                    self.save_current_article()
                    
                    current_subsection = self.create_subsection(text, len(self.book_structure['subsections']) + 1, current_section)
                    self.book_structure['subsections'].append(current_subsection)
                    print(f"🔸 مبحث: {text[:50]}...")
                    
                    # بدء مقال جديد للمبحث
                    self.start_new_article(text, current_subsection, current_section, current_chapter, current_part)
                    
                else:
                    # محتوى المقال - تحسين شروط القبول
                    if self.is_valid_content(text):
                        self.add_to_current_article(text)
                    else:
                        # تتبع المحتوى المتجاهل للتحليل
                        if len(text) > 10:  # تجاهل النصوص القصيرة جداً فقط
                            self.ignored_content.append({
                                'text': text[:100],
                                'length': len(text),
                                'words': len(text.split())
                            })
            
            # حفظ آخر مقال
            self.save_current_article()
            
            self.print_enhanced_analysis_summary()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحليل الكتاب: {e}")
            return False
    
    def classify_text_enhanced(self, text):
        """تصنيف النص المحسن مع أنماط أكثر شمولية"""
        
        # الأقسام الرئيسية - أنماط محسنة
        part_patterns = [
            r'^القسم\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|الحادي عشر|الثاني عشر|الثالث عشر|الرابع عشر|الخامس عشر|السادس عشر|السابع عشر|الثامن عشر|التاسع عشر|العشرون)',
            r'^الجزء\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^المرحلة\s+(الأولى|الثانية|الثالثة|الرابعة|الخامسة)',
            r'^الباب\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس)\s*:',  # أبواب رئيسية
        ]
        
        # الأبواب - أنماط محسنة
        chapter_patterns = [
            r'^الباب\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)',
            r'^المحور\s+(الأول|الثاني|الثالث|الرابع|الخامس)',
            r'^الباب\s+\d+',  # أبواب بأرقام
        ]
        
        # الفصول - أنماط محسنة
        section_patterns = [
            r'^الفصل\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|الحادي عشر|الثاني عشر|الثالث عشر|الرابع عشر|الخامس عشر|السادس عشر|السابع عشر|الثامن عشر|التاسع عشر|العشرون|الحادي والعشرون|الثاني والعشرون|الثالث والعشرون|الرابع والعشرون|الخامس والعشرون|السادس والعشرون|السابع والعشرون|الثامن والعشرون|التاسع والعشرون|الثلاثون)',
            r'^القضية\s+(الأولى|الثانية|الثالثة|الرابعة|الخامسة)',
            r'^الفصل\s+\d+',  # فصول بأرقام
        ]
        
        # المباحث - أنماط محسنة
        subsection_patterns = [
            r'^المبحث\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن)',
            r'^أولاً[:：]',
            r'^ثانياً[:：]',
            r'^ثالثاً[:：]',
            r'^رابعاً[:：]',
            r'^خامساً[:：]',
            r'^سادساً[:：]',
            r'^سابعاً[:：]',
            r'^ثامناً[:：]',
            r'^\d+\.\s*',  # نقاط مرقمة
            r'^[أ-ي]\.\s*',  # نقاط بحروف عربية
        ]
        
        # فحص الأنماط
        for pattern in part_patterns:
            if re.search(pattern, text):
                return 'part'
        
        for pattern in chapter_patterns:
            if re.search(pattern, text):
                return 'chapter'
        
        for pattern in section_patterns:
            if re.search(pattern, text):
                return 'section'
        
        for pattern in subsection_patterns:
            if re.search(pattern, text):
                return 'subsection'
        
        return 'content'
    
    def is_valid_content(self, text):
        """تحديد ما إذا كان النص محتوى صالح للإدراج"""
        
        # تجاهل النصوص القصيرة جداً
        if len(text) < 10:
            return False
        
        # تجاهل النصوص التي تحتوي على أرقام صفحات فقط
        if re.match(r'^\d+$', text.strip()):
            return False
        
        # تجاهل النصوص التي تحتوي على تواريخ فقط
        if re.match(r'^\d{4}[-/]\d{1,2}[-/]\d{1,2}$', text.strip()):
            return False
        
        # تجاهل النصوص التي تحتوي على رموز فقط
        if re.match(r'^[^\w\s]+$', text.strip()):
            return False
        
        # قبول النصوص التي تحتوي على كلمات عربية أو إنجليزية
        if re.search(r'[أ-ي]|[a-zA-Z]', text):
            return True
        
        return False
    
    def create_part(self, title, part_id):
        """إنشاء قسم رئيسي"""
        return {
            'id': part_id,
            'title': title,
            'type': 'part',
            'level': 1,
            'children': []
        }
    
    def create_chapter(self, title, chapter_id, parent_part):
        """إنشاء باب"""
        chapter = {
            'id': chapter_id,
            'title': title,
            'type': 'chapter',
            'level': 2,
            'parent_id': parent_part['id'] if parent_part else None,
            'children': []
        }
        
        if parent_part:
            parent_part['children'].append(chapter_id)
        
        return chapter
    
    def create_section(self, title, section_id, parent_chapter):
        """إنشاء فصل"""
        section = {
            'id': section_id,
            'title': title,
            'type': 'section',
            'level': 3,
            'parent_id': parent_chapter['id'] if parent_chapter else None,
            'children': []
        }
        
        if parent_chapter:
            parent_chapter['children'].append(section_id)
        
        return section
    
    def create_subsection(self, title, subsection_id, parent_section):
        """إنشاء مبحث"""
        subsection = {
            'id': subsection_id,
            'title': title,
            'type': 'subsection',
            'level': 4,
            'parent_id': parent_section['id'] if parent_section else None,
            'children': []
        }
        
        if parent_section:
            parent_section['children'].append(subsection_id)
        
        return subsection
    
    def start_new_article(self, title, current_level, *parent_levels):
        """بدء مقال جديد"""
        # حفظ المقال السابق إن وجد
        self.save_current_article()
        
        # تحديد المستوى الأب
        parent_id = None
        parent_type = None
        
        if current_level:
            parent_id = current_level['id']
            parent_type = current_level['type']
        
        # إنشاء مقال جديد
        self.current_article = {
            'id': len(self.book_structure['articles']) + 1,
            'title': title,
            'type': 'article',
            'level': 5,
            'parent_id': parent_id,
            'parent_type': parent_type,
            'word_count': 0
        }
        
        self.article_content = []
        
        # إضافة العنوان كأول محتوى
        self.add_to_current_article(title)
    
    def add_to_current_article(self, text):
        """إضافة نص للمقال الحالي"""
        if self.current_article is None:
            # إنشاء مقال افتراضي إذا لم يكن هناك مقال حالي
            self.current_article = {
                'id': len(self.book_structure['articles']) + 1,
                'title': "مقدمة الكتاب",
                'type': 'article',
                'level': 5,
                'parent_id': None,
                'parent_type': None,
                'word_count': 0
            }
            self.article_content = []
        
        self.article_content.append(text)
        
        # حساب عدد الكلمات التقريبي
        words = len(text.split())
        self.current_article['word_count'] += words
    
    def save_current_article(self):
        """حفظ المقال الحالي"""
        if self.current_article and self.article_content:
            # دمج المحتوى
            full_content = '\n\n'.join(self.article_content)
            
            # تحديث المقال
            self.current_article['content'] = full_content
            self.current_article['excerpt'] = self.create_excerpt(full_content)
            
            # إضافة للقائمة
            self.book_structure['articles'].append(self.current_article)
            
            # إضافة للمستوى الأب
            if self.current_article.get('parent_type') and self.current_article.get('parent_id'):
                parent_type = self.current_article['parent_type']
                parent_id = self.current_article['parent_id']
                
                if parent_type == 'subsection':
                    parent = next((s for s in self.book_structure['subsections'] if s['id'] == parent_id), None)
                elif parent_type == 'section':
                    parent = next((s for s in self.book_structure['sections'] if s['id'] == parent_id), None)
                elif parent_type == 'chapter':
                    parent = next((c for c in self.book_structure['chapters'] if c['id'] == parent_id), None)
                elif parent_type == 'part':
                    parent = next((p for p in self.book_structure['parts'] if p['id'] == parent_id), None)
                else:
                    parent = None
                
                if parent:
                    parent['children'].append(self.current_article['id'])
            
            print(f"💾 حُفظ مقال: {self.current_article['title'][:50]}... ({self.current_article['word_count']} كلمة)")
            
            # إعادة تعيين
            self.current_article = None
            self.article_content = []
    
    def create_excerpt(self, content, max_length=300):
        """إنشاء مقتطف من المحتوى"""
        if len(content) <= max_length:
            return content
        
        # البحث عن نقطة قطع مناسبة
        excerpt = content[:max_length]
        last_sentence = excerpt.rfind('.')
        last_space = excerpt.rfind(' ')
        
        if last_sentence > max_length - 100:
            return content[:last_sentence + 1]
        elif last_space > max_length - 50:
            return content[:last_space] + "..."
        else:
            return content[:max_length - 3] + "..."
    
    def print_enhanced_analysis_summary(self):
        """طباعة ملخص التحليل المحسن"""
        print(f"\n📊 ملخص تحليل الكتاب المحسن (مقالات كاملة):")
        print(f"   📚 الأقسام الرئيسية: {len(self.book_structure['parts'])}")
        print(f"   📖 الأبواب: {len(self.book_structure['chapters'])}")
        print(f"   📝 الفصول: {len(self.book_structure['sections'])}")
        print(f"   🔸 المباحث: {len(self.book_structure['subsections'])}")
        print(f"   📄 المقالات الكاملة: {len(self.book_structure['articles'])}")
        
        # إحصائيات الكلمات
        if self.book_structure['articles']:
            word_counts = [article['word_count'] for article in self.book_structure['articles']]
            avg_words = sum(word_counts) / len(word_counts)
            min_words = min(word_counts)
            max_words = max(word_counts)
            total_words = sum(word_counts)
            
            print(f"\n📊 إحصائيات الكلمات:")
            print(f"   📈 متوسط الكلمات: {avg_words:.0f} كلمة/مقال")
            print(f"   📉 أقل مقال: {min_words} كلمة")
            print(f"   📈 أكبر مقال: {max_words} كلمة")
            print(f"   📊 إجمالي الكلمات: {total_words:,} كلمة")
        
        # إحصائيات المحتوى المتجاهل
        if self.ignored_content:
            ignored_words = sum(item['words'] for item in self.ignored_content)
            print(f"\n⚠️ المحتوى المتجاهل:")
            print(f"   📄 عدد النصوص المتجاهلة: {len(self.ignored_content)}")
            print(f"   📚 كلمات متجاهلة: {ignored_words:,}")
            
            # عرض عينة من المحتوى المتجاهل
            print(f"   📋 عينة من المحتوى المتجاهل:")
            for i, item in enumerate(self.ignored_content[:5]):
                print(f"      {i+1}. {item['text']} ({item['words']} كلمة)")
    
    def save_enhanced_structure(self):
        """حفظ البنية المحسنة في ملفات JSON"""
        # حفظ البنية الأصلية
        with open('book_structure_enhanced.json', 'w', encoding='utf-8') as f:
            json.dump(self.book_structure, f, ensure_ascii=False, indent=2)
        
        # حفظ المحتوى المتجاهل للمراجعة
        with open('ignored_content_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(self.ignored_content, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ البنية المحسنة في: book_structure_enhanced.json")
        print(f"✅ تم حفظ تحليل المحتوى المتجاهل في: ignored_content_analysis.json")

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء مقالات كاملة محسنة لكتاب النهضة")
    print("=" * 70)
    
    # إنشاء مولد المقالات الكاملة المحسن
    generator = EnhancedCompleteArticleGenerator()
    
    # تحليل الكتاب
    if not generator.analyze_book_enhanced('../Nahda.docx'):
        print("❌ فشل في تحليل الكتاب")
        return False
    
    # حفظ البنية
    generator.save_enhanced_structure()
    
    print(f"\n🎉 تم تحليل الكتاب وإنشاء المقالات الكاملة المحسنة بنجاح!")
    print(f"الخطوة التالية: إنشاء ملفات XML للمقالات الكاملة المحسنة")
    
    return True

if __name__ == "__main__":
    main()
