-- Fix Categories SQL for WordPress
-- This will create proper categories and assign posts to them
SET NAMES utf8mb4;

-- إنشاء التصنيفات الجديدة

-- إنشاء الباب: الباب الأول...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (100, 'الباب الأول', 'الباب-الأول', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (100, 100, 'category', '', 0, 0);

-- إنشاء الباب: فتح الباب أمام التجديد والاجتهاد....
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (101, 'فتح الباب أمام التجديد والاجتهاد.', 'فتح-الباب-أمام-التجديد-والاجتهاد', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (101, 101, 'category', '', 0, 0);

-- إنشاء الباب: خاتمة الفصل وختام الباب الأول : من الجهل المؤدلج إ...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (102, 'خاتمة الفصل وختام الباب الأول : من الجهل المؤدلج إلى المعرفة المُحرّرة', 'خاتمة-الفصل-وختام-الباب-الأول-من-الجهل-المؤدلج-إلى', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (102, 102, 'category', '', 0, 0);

-- إنشاء الباب: الباب الثاني...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (103, 'الباب الثاني', 'الباب-الثاني', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (103, 103, 'category', '', 0, 0);

-- إنشاء الباب: خاتمة الفصل وخاتمة الباب الثاني:...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (104, 'خاتمة الفصل وخاتمة الباب الثاني:', 'خاتمة-الفصل-وخاتمة-الباب-الثاني', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (104, 104, 'category', '', 0, 0);

-- إنشاء الباب: الباب الثالث...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (105, 'الباب الثالث', 'الباب-الثالث', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (105, 105, 'category', '', 0, 0);

-- إنشاء الباب: خاتمة الفصل وخاتمة الباب الثالث: من الألم إلى الفع...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (106, 'خاتمة الفصل وخاتمة الباب الثالث: من الألم إلى الفعل – الشفاء بوصفه شرطًا للنهضة', 'خاتمة-الفصل-وخاتمة-الباب-الثالث-من-الألم-إلى-الفعل', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (106, 106, 'category', '', 0, 0);

-- إنشاء الباب: الباب الرابع...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (107, 'الباب الرابع', 'الباب-الرابع', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (107, 107, 'category', '', 0, 0);

-- إنشاء الباب: ● خاتمة الفصل وخاتمة الباب الرابع:...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (108, '● خاتمة الفصل وخاتمة الباب الرابع:', 'خاتمة-الفصل-وخاتمة-الباب-الرابع', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (108, 108, 'category', '', 0, 0);

-- إنشاء الباب: الباب الخامس...
INSERT IGNORE INTO wp_terms (term_id, name, slug, term_group) 
VALUES (109, 'الباب الخامس', 'الباب-الخامس', 0);

INSERT IGNORE INTO wp_term_taxonomy (term_taxonomy_id, term_id, taxonomy, description, parent, count) 
VALUES (109, 109, 'category', '', 0, 0);

-- إزالة التصنيفات الخاطئة من المقالات

DELETE FROM wp_term_relationships 
WHERE object_id IN (
    SELECT ID FROM wp_posts 
    WHERE post_type = 'post' 
    AND post_status = 'publish'
    AND post_title LIKE '%الباب%' 
    OR post_title LIKE '%الفصل%'
    OR post_title LIKE '%المبحث%'
);

-- تعيين التصنيفات الصحيحة للمقالات

-- تحديث عدد المقالات في كل تصنيف

UPDATE wp_term_taxonomy tt
SET count = (
    SELECT COUNT(*) 
    FROM wp_term_relationships tr 
    WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
)
WHERE tt.taxonomy = 'category';

-- إزالة تصنيف 'غير مصنف' من المقالات التي لها تصنيفات أخرى

DELETE tr1 FROM wp_term_relationships tr1
INNER JOIN wp_term_taxonomy tt1 ON tr1.term_taxonomy_id = tt1.term_taxonomy_id
INNER JOIN wp_terms t1 ON tt1.term_id = t1.term_id
WHERE t1.slug = 'uncategorized'
AND tr1.object_id IN (
    SELECT DISTINCT tr2.object_id 
    FROM wp_term_relationships tr2
    INNER JOIN wp_term_taxonomy tt2 ON tr2.term_taxonomy_id = tt2.term_taxonomy_id
    INNER JOIN wp_terms t2 ON tt2.term_id = t2.term_id
    WHERE t2.slug != 'uncategorized'
);