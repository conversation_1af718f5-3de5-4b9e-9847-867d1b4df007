#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل اكتمال محتوى الكتاب
التحقق من أن جميع أجزاء الكتاب تم استخراجها
"""

from docx import Document
import json
import re
from datetime import datetime

def analyze_original_book():
    """تحليل الكتاب الأصلي"""
    print("🔍 تحليل الكتاب الأصلي...")

    try:
        doc = Document('../Nahda.docx')

        total_paragraphs = len(doc.paragraphs)
        non_empty_paragraphs = [p for p in doc.paragraphs if p.text.strip()]

        # حساب الكلمات
        total_words = 0
        for para in non_empty_paragraphs:
            words = len(para.text.split())
            total_words += words

        # تحليل العناوين
        parts_found = []
        chapters_found = []
        sections_found = []

        for para in non_empty_paragraphs:
            text = para.text.strip()

            # البحث عن الأقسام
            if re.search(r'القسم\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|الحادي عشر|الثاني عشر|الثالث عشر|الرابع عشر|الخامس عشر|السادس عشر)', text):
                parts_found.append(text[:100])

            # البحث عن الأبواب
            if re.search(r'الباب\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر)', text):
                chapters_found.append(text[:100])

            # البحث عن الفصول
            if re.search(r'الفصل\s+(الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|الحادي عشر|الثاني عشر|الثالث عشر|الرابع عشر|الخامس عشر|السادس عشر|السابع عشر|الثامن عشر|التاسع عشر|العشرون)', text):
                sections_found.append(text[:100])

        print(f"📊 إحصائيات الكتاب الأصلي:")
        print(f"   📄 إجمالي الفقرات: {total_paragraphs:,}")
        print(f"   📝 الفقرات غير الفارغة: {len(non_empty_paragraphs):,}")
        print(f"   📚 إجمالي الكلمات: {total_words:,}")
        print(f"   📚 الأقسام المكتشفة: {len(parts_found)}")
        print(f"   📖 الأبواب المكتشفة: {len(chapters_found)}")
        print(f"   📝 الفصول المكتشفة: {len(sections_found)}")

        return {
            'total_paragraphs': total_paragraphs,
            'non_empty_paragraphs': len(non_empty_paragraphs),
            'total_words': total_words,
            'parts_found': len(parts_found),
            'chapters_found': len(chapters_found),
            'sections_found': len(sections_found),
            'parts_list': parts_found,
            'chapters_list': chapters_found,
            'sections_list': sections_found
        }

    except Exception as e:
        print(f"❌ خطأ في تحليل الكتاب: {e}")
        return None

def analyze_extracted_content():
    """تحليل المحتوى المستخرج"""
    print("\n🔍 تحليل المحتوى المستخرج...")

    try:
        with open('book_structure_complete.json', 'r', encoding='utf-8') as f:
            data = json.load(f)

        parts = data['parts']
        chapters = data['chapters']
        sections = data['sections']
        subsections = data['subsections']
        articles = data['articles']

        # حساب الكلمات المستخرجة
        extracted_words = sum(article.get('word_count', 0) for article in articles)

        print(f"📊 إحصائيات المحتوى المستخرج:")
        print(f"   📚 الأقسام: {len(parts)}")
        print(f"   📖 الأبواب: {len(chapters)}")
        print(f"   📝 الفصول: {len(sections)}")
        print(f"   🔸 المباحث: {len(subsections)}")
        print(f"   📄 المقالات: {len(articles)}")
        print(f"   📚 الكلمات المستخرجة: {extracted_words:,}")

        return {
            'parts': len(parts),
            'chapters': len(chapters),
            'sections': len(sections),
            'subsections': len(subsections),
            'articles': len(articles),
            'extracted_words': extracted_words
        }

    except Exception as e:
        print(f"❌ خطأ في تحليل المحتوى المستخرج: {e}")
        return None

def find_missing_content(original_stats, extracted_stats):
    """البحث عن المحتوى المفقود"""
    print("\n🔍 البحث عن المحتوى المفقود...")

    if not original_stats or not extracted_stats:
        print("❌ لا يمكن المقارنة بسبب خطأ في البيانات")
        return

    # مقارنة الكلمات
    word_coverage = (extracted_stats['extracted_words'] / original_stats['total_words']) * 100

    print(f"📊 تحليل الاكتمال:")
    print(f"   📚 الكلمات الأصلية: {original_stats['total_words']:,}")
    print(f"   📚 الكلمات المستخرجة: {extracted_stats['extracted_words']:,}")
    print(f"   📊 نسبة التغطية: {word_coverage:.1f}%")

    if word_coverage < 80:
        print(f"⚠️ تحذير: نسبة التغطية منخفضة ({word_coverage:.1f}%)")
        print(f"   🔍 قد يكون هناك محتوى مفقود")

        # مقارنة العناوين
        print(f"\n📋 مقارنة العناوين:")
        print(f"   📚 الأقسام - الأصلي: {original_stats['parts_found']}, المستخرج: {extracted_stats['parts']}")
        print(f"   📖 الأبواب - الأصلي: {original_stats['chapters_found']}, المستخرج: {extracted_stats['chapters']}")
        print(f"   📝 الفصول - الأصلي: {original_stats['sections_found']}, المستخرج: {extracted_stats['sections']}")

        # اقتراحات للتحسين
        print(f"\n💡 اقتراحات للتحسين:")
        print(f"   1. تحسين خوارزمية التعرف على العناوين")
        print(f"   2. فحص النصوص المتجاهلة (أقل من 30 كلمة)")
        print(f"   3. تحسين معالجة الجداول والقوائم")
        print(f"   4. فحص النصوص في الهوامش والملاحق")

    else:
        print(f"✅ تغطية جيدة: {word_coverage:.1f}%")

    return word_coverage

def create_detailed_report(original_stats, extracted_stats, coverage):
    """إنشاء تقرير مفصل"""

    report = f"""# 📊 تقرير تحليل اكتمال كتاب النهضة

## 🎯 الهدف
التحقق من اكتمال استخراج محتوى كتاب "مشروع النهضة وبناء الدولة السورية".

## 📋 النتائج الرئيسية

### 📚 **الكتاب الأصلي:**
- **إجمالي الفقرات:** {original_stats['total_paragraphs']:,}
- **الفقرات غير الفارغة:** {original_stats['non_empty_paragraphs']:,}
- **إجمالي الكلمات:** {original_stats['total_words']:,}
- **الأقسام المكتشفة:** {original_stats['parts_found']}
- **الأبواب المكتشفة:** {original_stats['chapters_found']}
- **الفصول المكتشفة:** {original_stats['sections_found']}

### 📄 **المحتوى المستخرج:**
- **الأقسام:** {extracted_stats['parts']}
- **الأبواب:** {extracted_stats['chapters']}
- **الفصول:** {extracted_stats['sections']}
- **المباحث:** {extracted_stats['subsections']}
- **المقالات:** {extracted_stats['articles']}
- **الكلمات المستخرجة:** {extracted_stats['extracted_words']:,}

### 📊 **نسبة التغطية:**
- **{coverage:.1f}%** من إجمالي كلمات الكتاب

## 🎯 التقييم

"""

    if coverage >= 90:
        report += """### ✅ **تغطية ممتازة (90%+)**
- المحتوى المستخرج شامل وكامل
- جميع الأجزاء الرئيسية مُغطاة
- جودة عالية في الاستخراج
"""
    elif coverage >= 80:
        report += """### ✅ **تغطية جيدة (80-90%)**
- المحتوى المستخرج شامل إلى حد كبير
- معظم الأجزاء الرئيسية مُغطاة
- قد يكون هناك محتوى ثانوي مفقود
"""
    elif coverage >= 70:
        report += """### ⚠️ **تغطية متوسطة (70-80%)**
- المحتوى المستخرج يغطي الجزء الأكبر
- قد يكون هناك أجزاء مهمة مفقودة
- يُنصح بمراجعة خوارزمية الاستخراج
"""
    else:
        report += """### ❌ **تغطية منخفضة (أقل من 70%)**
- المحتوى المستخرج ناقص بشكل كبير
- أجزاء مهمة من الكتاب مفقودة
- يجب إعادة تطوير خوارزمية الاستخراج
"""

    report += f"""

## 🔍 تحليل مفصل

### **مقارنة العناوين:**
| النوع | الأصلي | المستخرج | النسبة |
|-------|--------|----------|--------|
| 📚 الأقسام | {original_stats['parts_found']} | {extracted_stats['parts']} | {(extracted_stats['parts']/original_stats['parts_found']*100):.1f}% |
| 📖 الأبواب | {original_stats['chapters_found']} | {extracted_stats['chapters']} | {(extracted_stats['chapters']/original_stats['chapters_found']*100):.1f}% |
| 📝 الفصول | {original_stats['sections_found']} | {extracted_stats['sections']} | {(extracted_stats['sections']/original_stats['sections_found']*100):.1f}% |

### **توزيع المحتوى:**
- **متوسط الكلمات/مقال:** {extracted_stats['extracted_words']//extracted_stats['articles']} كلمة
- **كثافة المحتوى:** {(extracted_stats['extracted_words']/original_stats['non_empty_paragraphs']):.1f} كلمة/فقرة

## 💡 التوصيات

"""

    if coverage < 80:
        report += """### **لتحسين التغطية:**
1. **مراجعة خوارزمية التعرف على العناوين**
2. **تحسين معالجة النصوص القصيرة**
3. **فحص الجداول والقوائم المتجاهلة**
4. **معالجة الهوامش والملاحق**
5. **تحسين تصنيف أنواع النصوص**
"""
    else:
        report += """### **للحفاظ على الجودة:**
1. **مراجعة دورية للمحتوى المستخرج**
2. **تحسين جودة التصنيف**
3. **تطوير آليات التحقق التلقائي**
"""

    report += f"""

## 🎉 الخلاصة

تم استخراج **{coverage:.1f}%** من محتوى كتاب النهضة بنجاح، مما ينتج عنه **{extracted_stats['articles']} مقال** كامل ومتماسك موزع على **{extracted_stats['parts']} قسم** رئيسي.

---

**📅 تاريخ التحليل:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    with open('BOOK_COMPLETENESS_REPORT.md', 'w', encoding='utf-8') as f:
        f.write(report)

    print("✅ تم إنشاء: BOOK_COMPLETENESS_REPORT.md")

def main():
    """الدالة الرئيسية"""
    print("🔍 تحليل اكتمال محتوى كتاب النهضة")
    print("=" * 60)

    # تحليل الكتاب الأصلي
    original_stats = analyze_original_book()

    # تحليل المحتوى المستخرج
    extracted_stats = analyze_extracted_content()

    # البحث عن المحتوى المفقود
    coverage = find_missing_content(original_stats, extracted_stats)

    # إنشاء تقرير مفصل
    if original_stats and extracted_stats and coverage:
        create_detailed_report(original_stats, extracted_stats, coverage)

    print(f"\n🎉 تم الانتهاء من التحليل!")

    return coverage

if __name__ == "__main__":
    main()
