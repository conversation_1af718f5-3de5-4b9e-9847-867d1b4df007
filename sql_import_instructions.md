
# تعليمات استيراد ملف SQL إلى WordPress

## الطريقة الأولى: phpMyAdmin
1. ادخل إلى phpMyAdmin
2. اختر قاعدة بيانات WordPress
3. اذه<PERSON> إلى تبويب "Import"
4. اختر ملف wordpress_import.sql
5. تأكد من Character set: utf8mb4_unicode_ci
6. اضغط "Go"

## الطريقة الثانية: سطر الأوامر
```bash
mysql -u username -p database_name < wordpress_import.sql
```

## الطريقة الثالثة: إضافة WordPress
استخدم إضافة "WP-DB Manager" أو "phpMyAdmin"

## بعد الاستيراد:
1. تحقق من Posts > Categories
2. تحقق من Posts > All Posts  
3. تأكد من ظهور التصنيفات الصحيحة
4. اختبر عرض المقالات

## ملاحظات مهمة:
- تأكد من أن قاعدة البيانات تدعم utf8mb4
- انشئ نسخة احتياطية قبل الاستيراد
- تأكد من صلاحيات المستخدم للكتابة في قاعدة البيانات
