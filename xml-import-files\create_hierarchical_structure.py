#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء البنية الهرمية الصحيحة - كتاب النهضة
إصلاح النظام الشجري للتصنيفات: مقدمة → قسم → باب → فصل
"""

import json
import re
from datetime import datetime

class HierarchicalStructureCreator:
    """منشئ البنية الهرمية الصحيحة"""
    
    def __init__(self):
        self.wp_categories = []
        self.wp_posts = []
        self.category_id_counter = 1
        self.post_id_counter = 1
        
    def load_organized_structure(self):
        """تحميل البنية المنظمة"""
        try:
            with open('book_structure_organized.json', 'r', encoding='utf-8') as f:
                self.book_structure = json.load(f)
            return True
        except Exception as e:
            print(f"❌ خطأ في تحميل البنية: {e}")
            return False
    
    def create_hierarchical_categories(self):
        """إنشاء التصنيفات الهرمية الصحيحة"""
        print("🏗️ إنشاء البنية الهرمية الصحيحة...")
        
        # 1. إنشاء تصنيف المقدمة
        if self.book_structure['introduction']:
            intro_cat = self.create_category(
                name='مقدمة الكتاب',
                slug='introduction',
                parent_id=0,
                level=1
            )
            self.wp_categories.append(intro_cat)
            
            # إنشاء مقال المقدمة
            intro_post = self.create_post(
                title=self.book_structure['introduction']['title'],
                content=self.book_structure['introduction']['html_content'],
                category_id=intro_cat['id'],
                word_count=self.book_structure['introduction']['word_count']
            )
            self.wp_posts.append(intro_post)
        
        # 2. إنشاء الأقسام الرئيسية
        part_mapping = {}
        for part in self.book_structure['parts']:
            part_cat = self.create_category(
                name=self.clean_title(part['title']),
                slug=self.create_slug(part['title']),
                parent_id=0,
                level=1
            )
            self.wp_categories.append(part_cat)
            part_mapping[part['id']] = part_cat['id']
            print(f"📚 قسم: {part_cat['name']}")
        
        # 3. إنشاء الأبواب تحت الأقسام
        chapter_mapping = {}
        for chapter in self.book_structure['chapters']:
            parent_wp_id = 0
            if chapter.get('parent_part_id') and chapter['parent_part_id'] in part_mapping:
                parent_wp_id = part_mapping[chapter['parent_part_id']]
            
            chapter_cat = self.create_category(
                name=self.clean_title(chapter['title']),
                slug=self.create_slug(chapter['title']),
                parent_id=parent_wp_id,
                level=2
            )
            self.wp_categories.append(chapter_cat)
            chapter_mapping[chapter['id']] = chapter_cat['id']
            print(f"  📖 باب: {chapter_cat['name']} (تحت قسم {parent_wp_id})")
        
        # 4. إنشاء مقالات الفصول
        for section in self.book_structure['sections']:
            # تحديد التصنيف الأب
            category_id = 1  # افتراضي للمقدمة
            
            if section.get('parent_chapter_id') and section['parent_chapter_id'] in chapter_mapping:
                # الفصل تحت باب
                category_id = chapter_mapping[section['parent_chapter_id']]
            elif section.get('parent_part_id') and section['parent_part_id'] in part_mapping:
                # الفصل مباشرة تحت قسم (بدون باب)
                category_id = part_mapping[section['parent_part_id']]
            
            section_post = self.create_post(
                title=self.clean_title(section['title']),
                content=section['html_content'],
                category_id=category_id,
                word_count=section['word_count']
            )
            self.wp_posts.append(section_post)
        
        print(f"✅ تم إنشاء البنية الهرمية:")
        print(f"   📂 التصنيفات: {len(self.wp_categories)}")
        print(f"   📄 المقالات: {len(self.wp_posts)}")
    
    def create_category(self, name, slug, parent_id, level):
        """إنشاء تصنيف"""
        category = {
            'id': self.category_id_counter,
            'name': name,
            'slug': slug,
            'parent': parent_id,
            'level': level
        }
        self.category_id_counter += 1
        return category
    
    def create_post(self, title, content, category_id, word_count):
        """إنشاء مقال"""
        post = {
            'id': self.post_id_counter,
            'title': title,
            'content': content,
            'category_id': category_id,
            'slug': self.create_slug(title),
            'excerpt': self.create_excerpt(content),
            'word_count': word_count
        }
        self.post_id_counter += 1
        return post
    
    def clean_title(self, title):
        """تنظيف العنوان"""
        # إزالة الأحرف الخاصة والتحكم في الطول
        cleaned = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', '', title)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned[:100]  # تحديد الطول
    
    def create_slug(self, text):
        """إنشاء slug للروابط"""
        # تنظيف النص وإنشاء slug
        slug = re.sub(r'[^\w\s-]', '', text)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-').lower()[:50]
    
    def create_excerpt(self, content, max_length=300):
        """إنشاء مقتطف من المحتوى"""
        # إزالة HTML tags للمقتطف
        text_content = re.sub(r'<[^>]+>', '', content)
        
        if len(text_content) <= max_length:
            return text_content
        
        # البحث عن نقطة قطع مناسبة
        excerpt = text_content[:max_length]
        last_sentence = excerpt.rfind('.')
        last_space = excerpt.rfind(' ')
        
        if last_sentence > max_length - 100:
            return text_content[:last_sentence + 1]
        elif last_space > max_length - 50:
            return text_content[:last_space] + "..."
        else:
            return text_content[:max_length - 3] + "..."
    
    def create_xml_header(self):
        """إنشاء رأس ملف XML"""
        return '''<?xml version="1.0" encoding="UTF-8" ?>
<rss version="2.0"
    xmlns:excerpt="http://wordpress.org/export/1.2/excerpt/"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:wfw="http://wellformedweb.org/CommentAPI/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:wp="http://wordpress.org/export/1.2/">

<channel>
    <title>كتاب النهضة - البنية الهرمية الصحيحة</title>
    <link>http://localhost</link>
    <description>مشروع النهضة وبناء الدولة السورية - بنية شجرية صحيحة</description>
    <pubDate>{}</pubDate>
    <language>ar</language>
    <wp:wxr_version>1.2</wp:wxr_version>
    <wp:base_site_url>http://localhost</wp:base_site_url>
    <wp:base_blog_url>http://localhost</wp:base_blog_url>

'''.format(datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000'))
    
    def create_category_xml(self, category):
        """إنشاء XML للتصنيف"""
        return f'''    <wp:category>
        <wp:term_id>{category['id']}</wp:term_id>
        <wp:category_nicename>{category['slug']}</wp:category_nicename>
        <wp:category_parent>{category['parent']}</wp:category_parent>
        <wp:cat_name><![CDATA[{category['name']}]]></wp:cat_name>
    </wp:category>

'''
    
    def create_post_xml(self, post):
        """إنشاء XML للمقال"""
        category_name = self.get_category_name(post['category_id'])
        category_slug = self.get_category_slug(post['category_id'])
        
        return f'''    <item>
        <title><![CDATA[{post['title']}]]></title>
        <link>http://localhost/{post['slug']}/</link>
        <pubDate>{datetime.now().strftime('%a, %d %b %Y %H:%M:%S +0000')}</pubDate>
        <dc:creator><![CDATA[admin]]></dc:creator>
        <guid isPermaLink="false">http://localhost/?p={post['id']}</guid>
        <description></description>
        <content:encoded><![CDATA[{post['content']}]]></content:encoded>
        <excerpt:encoded><![CDATA[{post['excerpt']}]]></excerpt:encoded>
        <wp:post_id>{post['id']}</wp:post_id>
        <wp:post_date>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date>
        <wp:post_date_gmt>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</wp:post_date_gmt>
        <wp:comment_status>open</wp:comment_status>
        <wp:ping_status>open</wp:ping_status>
        <wp:post_name>{post['slug']}</wp:post_name>
        <wp:status>publish</wp:status>
        <wp:post_parent>0</wp:post_parent>
        <wp:menu_order>0</wp:menu_order>
        <wp:post_type>post</wp:post_type>
        <wp:post_password></wp:post_password>
        <wp:is_sticky>0</wp:is_sticky>
        <category domain="category" nicename="{category_slug}"><![CDATA[{category_name}]]></category>
    </item>

'''
    
    def get_category_name(self, category_id):
        """الحصول على اسم التصنيف"""
        category = next((cat for cat in self.wp_categories if cat['id'] == category_id), None)
        return category['name'] if category else 'غير محدد'
    
    def get_category_slug(self, category_id):
        """الحصول على slug التصنيف"""
        category = next((cat for cat in self.wp_categories if cat['id'] == category_id), None)
        return category['slug'] if category else 'undefined'
    
    def create_xml_footer(self):
        """إنشاء ذيل ملف XML"""
        return '''</channel>
</rss>'''
    
    def generate_test_file(self):
        """إنشاء ملف اختبار"""
        print("🧪 إنشاء ملف اختبار للبنية الهرمية...")
        
        # اختيار عينة من التصنيفات والمقالات
        test_categories = self.wp_categories[:15]
        test_posts = self.wp_posts[:10]
        
        xml_content = self.create_xml_header()
        
        # إضافة التصنيفات
        for category in test_categories:
            xml_content += self.create_category_xml(category)
        
        # إضافة المقالات
        for post in test_posts:
            xml_content += self.create_post_xml(post)
        
        xml_content += self.create_xml_footer()
        
        # حفظ الملف
        filename = 'test_hierarchical_structure.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        file_size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB)")
        print(f"   📊 المحتوى: {len(test_categories)} تصنيف + {len(test_posts)} مقال")
        
        return filename
    
    def generate_categories_file(self):
        """إنشاء ملف التصنيفات الهرمية"""
        print("📂 إنشاء ملف التصنيفات الهرمية...")
        
        xml_content = self.create_xml_header()
        
        # إضافة جميع التصنيفات
        for category in self.wp_categories:
            xml_content += self.create_category_xml(category)
        
        xml_content += self.create_xml_footer()
        
        # حفظ الملف
        filename = 'hierarchical_categories.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        file_size = os.path.getsize(filename) / 1024
        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB - {len(self.wp_categories)} تصنيف)")
        
        return filename
    
    def generate_posts_files(self, max_file_size_mb=2):
        """إنشاء ملفات المقالات"""
        print("📄 إنشاء ملفات المقالات الهرمية...")
        
        max_file_size = max_file_size_mb * 1024 * 1024  # تحويل إلى بايت
        files_created = []
        
        current_file_posts = []
        current_file_size = 0
        file_number = 1
        
        header_size = len(self.create_xml_header().encode('utf-8'))
        footer_size = len(self.create_xml_footer().encode('utf-8'))
        
        for post in self.wp_posts:
            post_xml = self.create_post_xml(post)
            post_size = len(post_xml.encode('utf-8'))
            
            # تحقق من حجم الملف
            if current_file_size + post_size + header_size + footer_size > max_file_size and current_file_posts:
                # حفظ الملف الحالي
                filename = self.save_posts_file(current_file_posts, file_number)
                files_created.append(filename)
                
                # بدء ملف جديد
                current_file_posts = [post]
                current_file_size = post_size
                file_number += 1
            else:
                current_file_posts.append(post)
                current_file_size += post_size
        
        # حفظ آخر ملف
        if current_file_posts:
            filename = self.save_posts_file(current_file_posts, file_number)
            files_created.append(filename)
        
        return files_created
    
    def save_posts_file(self, posts, file_number):
        """حفظ ملف المقالات"""
        xml_content = self.create_xml_header()
        
        for post in posts:
            xml_content += self.create_post_xml(post)
        
        xml_content += self.create_xml_footer()
        
        filename = f'hierarchical_posts_part_{file_number:02d}.xml'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        file_size = os.path.getsize(filename) / 1024
        avg_words = sum(post['word_count'] for post in posts) // len(posts) if posts else 0
        word_range = f"{min(post['word_count'] for post in posts)}-{max(post['word_count'] for post in posts)}" if posts else "0-0"
        
        print(f"✅ تم إنشاء: {filename} ({file_size:.1f} KB - {len(posts)} مقال)")
        print(f"   📊 متوسط الكلمات: {avg_words} كلمة/مقال")
        print(f"   📈 نطاق الكلمات: {word_range} كلمة")
        
        return filename
    
    def print_hierarchy_summary(self):
        """طباعة ملخص البنية الهرمية"""
        print(f"\n📊 ملخص البنية الهرمية الصحيحة:")
        
        # تحليل مستويات التصنيفات
        level_1_cats = [cat for cat in self.wp_categories if cat['parent'] == 0]
        level_2_cats = [cat for cat in self.wp_categories if cat['parent'] != 0]
        
        print(f"   📚 المستوى الأول (أقسام): {len(level_1_cats)}")
        print(f"   📖 المستوى الثاني (أبواب): {len(level_2_cats)}")
        print(f"   📄 المقالات (فصول): {len(self.wp_posts)}")
        
        # عرض عينة من البنية
        print(f"\n🌳 عينة من البنية الشجرية:")
        for level_1_cat in level_1_cats[:3]:
            print(f"📚 {level_1_cat['name']}")
            
            # البحث عن الأبواب التابعة لهذا القسم
            child_cats = [cat for cat in self.wp_categories if cat['parent'] == level_1_cat['id']]
            for child_cat in child_cats[:2]:
                print(f"  📖 {child_cat['name']}")
                
                # البحث عن المقالات التابعة لهذا الباب
                child_posts = [post for post in self.wp_posts if post['category_id'] == child_cat['id']]
                for post in child_posts[:2]:
                    print(f"    📄 {post['title'][:50]}...")
            
            # البحث عن المقالات المباشرة تحت القسم
            direct_posts = [post for post in self.wp_posts if post['category_id'] == level_1_cat['id']]
            for post in direct_posts[:2]:
                print(f"  📄 {post['title'][:50]}...")

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء البنية الهرمية الصحيحة لكتاب النهضة")
    print("=" * 70)
    print("🎯 الهدف: إصلاح النظام الشجري للتصنيفات")
    print("📋 البنية: مقدمة → قسم → باب → فصل (مقال كامل)")
    print("=" * 70)
    
    # إنشاء منشئ البنية الهرمية
    creator = HierarchicalStructureCreator()
    
    # تحميل البنية المنظمة
    if not creator.load_organized_structure():
        return False
    
    # إنشاء التصنيفات الهرمية
    creator.create_hierarchical_categories()
    
    # طباعة ملخص البنية
    creator.print_hierarchy_summary()
    
    print(f"\n🔧 إنشاء ملفات XML للبنية الهرمية...")
    
    # إنشاء ملف الاختبار
    test_file = creator.generate_test_file()
    
    # إنشاء ملف التصنيفات
    categories_file = creator.generate_categories_file()
    
    # إنشاء ملفات المقالات
    posts_files = creator.generate_posts_files()
    
    print(f"\n🎉 تم إنشاء البنية الهرمية الصحيحة بنجاح!")
    print(f"📁 الملفات المُنشأة: {1 + 1 + len(posts_files)} ملف")
    print(f"📂 التصنيفات الهرمية: {len(creator.wp_categories)}")
    print(f"📄 المقالات: {len(creator.wp_posts)}")
    
    return True

if __name__ == "__main__":
    import os
    main()
